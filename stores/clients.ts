import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { clientApi, type Client, type ClientsResponse, type ClientStatsResponse } from '~/services/clientApi'

export const useClientStore = defineStore('clients', () => {
  // State
  const clients = ref<Client[]>([])
  const currentClient = ref<Client | null>(null)
  const loading = ref(false)
  const error = ref<string>('')
  const stats = ref({
    totalClients: 0,
    activeClients: 0,
    inactiveClients: 0,
    totalInvoiceAmount: 0,
    totalOutstanding: 0
  })

  // Pagination state
  const pagination = ref({
    page: 1,
    limit: 10,
    totalPages: 0,
    totalClients: 0,
    hasNextPage: false,
    hasPrevPage: false
  })

  // Filters
  const filters = ref({
    search: '',
    status: '',
    sort: '-createdAt'
  })

  // Computed
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const clientsCount = computed(() => clients.value.length)
  const activeClientsCount = computed(() => 
    clients.value.filter(client => client.status === 'Active').length
  )

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string) => {
    error.value = message
  }

  const clearError = () => {
    error.value = ''
  }

  const fetchClients = async (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    sort?: string
  }) => {
    try {
      setLoading(true)
      clearError()

      const queryParams = {
        page: params?.page || pagination.value.page,
        limit: params?.limit || pagination.value.limit,
        search: params?.search || filters.value.search,
        status: params?.status || filters.value.status,
        sort: params?.sort || filters.value.sort
      }

      // Update filters
      if (params) {
        Object.assign(filters.value, params)
      }

      const response = await clientApi.getClients(queryParams)
      
      clients.value = response.data
      pagination.value = response.pagination

      return response
    } catch (err: any) {
      setError(err.message || 'Failed to fetch clients')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const fetchClient = async (id: string) => {
    try {
      setLoading(true)
      clearError()

      const response = await clientApi.getClient(id)
      currentClient.value = response.data

      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to fetch client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const createClient = async (clientData: Omit<Client, '_id'>) => {
    try {
      setLoading(true)
      clearError()

      const response = await clientApi.createClient(clientData)
      
      // Add to local state
      clients.value.unshift(response.data)
      
      // Update stats
      await fetchStats()

      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to create client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const updateClient = async (id: string, clientData: Partial<Client>) => {
    try {
      setLoading(true)
      clearError()

      const response = await clientApi.updateClient(id, clientData)
      
      // Update local state
      const index = clients.value.findIndex(client => client._id === id)
      if (index !== -1) {
        clients.value[index] = response.data
      }

      // Update current client if it's the same
      if (currentClient.value?._id === id) {
        currentClient.value = response.data
      }

      return response.data
    } catch (err: any) {
      setError(err.message || 'Failed to update client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const deleteClient = async (id: string) => {
    try {
      setLoading(true)
      clearError()

      await clientApi.deleteClient(id)
      
      // Remove from local state
      clients.value = clients.value.filter(client => client._id !== id)
      
      // Clear current client if it's the same
      if (currentClient.value?._id === id) {
        currentClient.value = null
      }

      // Update stats
      await fetchStats()

      return true
    } catch (err: any) {
      setError(err.message || 'Failed to delete client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await clientApi.getClientStats()
      stats.value = response.data
      return response.data
    } catch (err: any) {
      console.error('Failed to fetch client stats:', err)
      // Don't throw error for stats as it's not critical
    }
  }

  const searchClients = async (searchTerm: string) => {
    filters.value.search = searchTerm
    pagination.value.page = 1 // Reset to first page
    await fetchClients()
  }

  const filterByStatus = async (status: string) => {
    filters.value.status = status
    pagination.value.page = 1 // Reset to first page
    await fetchClients()
  }

  const sortClients = async (sortField: string) => {
    filters.value.sort = sortField
    pagination.value.page = 1 // Reset to first page
    await fetchClients()
  }

  const nextPage = async () => {
    if (pagination.value.hasNextPage) {
      pagination.value.page += 1
      await fetchClients()
    }
  }

  const prevPage = async () => {
    if (pagination.value.hasPrevPage) {
      pagination.value.page -= 1
      await fetchClients()
    }
  }

  const goToPage = async (page: number) => {
    pagination.value.page = page
    await fetchClients()
  }

  const refreshClients = async () => {
    await Promise.all([
      fetchClients(),
      fetchStats()
    ])
  }

  const clearCurrentClient = () => {
    currentClient.value = null
  }

  const resetFilters = async () => {
    filters.value = {
      search: '',
      status: '',
      sort: '-createdAt'
    }
    pagination.value.page = 1
    await fetchClients()
  }

  const clearSearch = () => {
    filters.value.search = ''
    pagination.value.page = 1
  }

  const setSearch = (searchTerm: string) => {
    filters.value.search = searchTerm
    pagination.value.page = 1
  }

  return {
    // State
    clients,
    currentClient,
    loading,
    error,
    stats,
    pagination,
    filters,

    // Computed
    isLoading,
    hasError,
    clientsCount,
    activeClientsCount,

    // Actions
    fetchClients,
    fetchClient,
    createClient,
    updateClient,
    deleteClient,
    fetchStats,
    searchClients,
    filterByStatus,
    sortClients,
    nextPage,
    prevPage,
    goToPage,
    refreshClients,
    clearCurrentClient,
    resetFilters,
    clearSearch,
    setSearch,
    setError,
    clearError
  }
})
