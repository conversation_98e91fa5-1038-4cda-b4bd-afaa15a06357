import { defineStore } from 'pinia'
import { expenseApi, type Expense, type ExpenseStats, type ExpenseParams } from '~/services/expenseApi'

interface ExpensesState {
  expenses: Expense[]
  currentExpense: Expense | null
  stats: ExpenseStats | null
  categories: string[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalExpenses: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null
}

export const useExpensesStore = defineStore('expenses', {
  state: (): ExpensesState => ({
    expenses: [],
    currentExpense: null,
    stats: null,
    categories: [],
    loading: false,
    error: null,
    pagination: null
  }),

  getters: {
    // Get expenses by status
    getExpensesByStatus: (state) => (status: string) => {
      if (status === 'all') return state.expenses
      return state.expenses.filter(expense => expense.status === status)
    },

    // Get expenses by category
    getExpensesByCategory: (state) => (category: string) => {
      if (category === 'all') return state.expenses
      return state.expenses.filter(expense => expense.category === category)
    },

    // Get expense by ID
    getExpenseById: (state) => (id: string) => {
      return state.expenses.find(expense => expense._id === id)
    },

    // Calculate total expense amount
    totalExpenseAmount: (state) => {
      return state.expenses.reduce((total, expense) => total + expense.totalAmount, 0)
    },

    // Get expenses count by status
    expensesCountByStatus: (state) => {
      const counts = {
        pending: 0,
        approved: 0,
        rejected: 0,
        paid: 0
      }
      
      state.expenses.forEach(expense => {
        if (counts.hasOwnProperty(expense.status)) {
          counts[expense.status as keyof typeof counts]++
        }
      })
      
      return counts
    },

    // Get pending expenses
    pendingExpenses: (state) => {
      return state.expenses.filter(expense => expense.status === 'pending')
    },

    // Get approved expenses
    approvedExpenses: (state) => {
      return state.expenses.filter(expense => expense.status === 'approved')
    },

    // Check if there are any expenses
    hasExpenses: (state) => state.expenses.length > 0,

    // Check if loading
    isLoading: (state) => state.loading,

    // Check if there's an error
    hasError: (state) => !!state.error
  },

  actions: {
    // Set loading state
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // Set error state
    setError(error: string | null) {
      this.error = error
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Fetch all expenses
    async fetchExpenses(params?: ExpenseParams) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await expenseApi.getExpenses(params)
        
        this.expenses = response.data
        this.pagination = response.pagination

        console.log('✅ Expenses fetched successfully:', response.data.length)
      } catch (error) {
        console.error('❌ Error fetching expenses:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch expenses')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch single expense
    async fetchExpense(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await expenseApi.getExpense(id)
        
        this.currentExpense = response.data

        // Update the expense in the list if it exists
        const index = this.expenses.findIndex(expense => expense._id === id)
        if (index !== -1) {
          this.expenses[index] = response.data
        }

        console.log('✅ Expense fetched successfully:', response.data.expenseId)
        return response.data
      } catch (error) {
        console.error('❌ Error fetching expense:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch expense')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Create new expense
    async createExpense(expenseData: Omit<Expense, '_id' | 'userId' | 'expenseId' | 'createdAt' | 'updatedAt'>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await expenseApi.createExpense(expenseData)
        
        // Add the new expense to the beginning of the list
        this.expenses.unshift(response.data)
        this.currentExpense = response.data

        console.log('✅ Expense created successfully:', response.data.expenseId)
        return response.data
      } catch (error) {
        console.error('❌ Error creating expense:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to create expense')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Update expense
    async updateExpense(id: string, expenseData: Partial<Expense>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await expenseApi.updateExpense(id, expenseData)
        
        // Update the expense in the list
        const index = this.expenses.findIndex(expense => expense._id === id)
        if (index !== -1) {
          this.expenses[index] = response.data
        }

        // Update current expense if it's the same
        if (this.currentExpense?._id === id) {
          this.currentExpense = response.data
        }

        console.log('✅ Expense updated successfully:', response.data.expenseId)
        return response.data
      } catch (error) {
        console.error('❌ Error updating expense:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to update expense')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Delete expense
    async deleteExpense(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        await expenseApi.deleteExpense(id)
        
        // Remove the expense from the list
        this.expenses = this.expenses.filter(expense => expense._id !== id)

        // Clear current expense if it's the deleted one
        if (this.currentExpense?._id === id) {
          this.currentExpense = null
        }

        console.log('✅ Expense deleted successfully')
      } catch (error) {
        console.error('❌ Error deleting expense:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to delete expense')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch expense statistics
    async fetchExpenseStats(startDate?: string, endDate?: string) {
      try {
        this.clearError()

        const response = await expenseApi.getExpenseStats(startDate, endDate)
        
        this.stats = response.data

        console.log('✅ Expense stats fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching expense stats:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch expense statistics')
        throw error
      }
    },

    // Fetch expense categories
    async fetchExpenseCategories() {
      try {
        this.clearError()

        const response = await expenseApi.getExpenseCategories()
        
        this.categories = response.data

        console.log('✅ Expense categories fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching expense categories:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch expense categories')
        throw error
      }
    },

    // Approve expense
    async approveExpense(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await expenseApi.approveExpense(id)
        
        // Update the expense in the list
        const index = this.expenses.findIndex(expense => expense._id === id)
        if (index !== -1) {
          this.expenses[index] = response.data
        }

        // Update current expense if it's the same
        if (this.currentExpense?._id === id) {
          this.currentExpense = response.data
        }

        console.log('✅ Expense approved successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error approving expense:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to approve expense')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Reject expense
    async rejectExpense(id: string, reason: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await expenseApi.rejectExpense(id, reason)
        
        // Update the expense in the list
        const index = this.expenses.findIndex(expense => expense._id === id)
        if (index !== -1) {
          this.expenses[index] = response.data
        }

        // Update current expense if it's the same
        if (this.currentExpense?._id === id) {
          this.currentExpense = response.data
        }

        console.log('✅ Expense rejected successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error rejecting expense:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to reject expense')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Set current expense
    setCurrentExpense(expense: Expense | null) {
      this.currentExpense = expense
    },

    // Clear all data
    clearData() {
      this.expenses = []
      this.currentExpense = null
      this.stats = null
      this.categories = []
      this.pagination = null
      this.error = null
    },

    // Refresh expenses (re-fetch with current params)
    async refreshExpenses() {
      await this.fetchExpenses()
    }
  }
})
