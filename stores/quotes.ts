import { defineStore } from 'pinia'
import { quoteApi, type Quote, type QuoteStats, type QuoteParams } from '~/services/quoteApi'

interface QuotesState {
  quotes: Quote[]
  currentQuote: Quote | null
  stats: QuoteStats | null
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalQuotes: number
    hasNextPage: boolean
    hasPrevPage: boolean
  } | null
}

export const useQuotesStore = defineStore('quotes', {
  state: (): QuotesState => ({
    quotes: [],
    currentQuote: null,
    stats: null,
    loading: false,
    error: null,
    pagination: null
  }),

  getters: {
    // Get quotes by status
    getQuotesByStatus: (state) => (status: string) => {
      if (status === 'all') return state.quotes
      return state.quotes.filter(quote => quote.status === status)
    },

    // Get quote by ID
    getQuoteById: (state) => (id: string) => {
      return state.quotes.find(quote => quote._id === id)
    },

    // Calculate total quote value
    totalQuoteValue: (state) => {
      return state.quotes.reduce((total, quote) => total + quote.total, 0)
    },

    // Get quotes count by status
    quotesCountByStatus: (state) => {
      const counts = {
        draft: 0,
        sent: 0,
        accepted: 0,
        rejected: 0,
        expired: 0
      }
      
      state.quotes.forEach(quote => {
        if (counts.hasOwnProperty(quote.status)) {
          counts[quote.status as keyof typeof counts]++
        }
      })
      
      return counts
    },

    // Check if there are any quotes
    hasQuotes: (state) => state.quotes.length > 0,

    // Check if loading
    isLoading: (state) => state.loading,

    // Check if there's an error
    hasError: (state) => !!state.error
  },

  actions: {
    // Set loading state
    setLoading(loading: boolean) {
      this.loading = loading
    },

    // Set error state
    setError(error: string | null) {
      this.error = error
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Fetch all quotes
    async fetchQuotes(params?: QuoteParams) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await quoteApi.getQuotes(params)
        
        this.quotes = response.data
        this.pagination = response.pagination

        console.log('✅ Quotes fetched successfully:', response.data.length)
      } catch (error) {
        console.error('❌ Error fetching quotes:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch quotes')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch single quote
    async fetchQuote(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await quoteApi.getQuote(id)
        
        this.currentQuote = response.data

        // Update the quote in the list if it exists
        const index = this.quotes.findIndex(quote => quote._id === id)
        if (index !== -1) {
          this.quotes[index] = response.data
        }

        console.log('✅ Quote fetched successfully:', response.data.quoteNumber)
        return response.data
      } catch (error) {
        console.error('❌ Error fetching quote:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch quote')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Create new quote
    async createQuote(quoteData: Omit<Quote, '_id' | 'userId' | 'createdAt' | 'updatedAt'>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await quoteApi.createQuote(quoteData)
        
        // Add the new quote to the beginning of the list
        this.quotes.unshift(response.data)
        this.currentQuote = response.data

        console.log('✅ Quote created successfully:', response.data.quoteNumber)
        return response.data
      } catch (error) {
        console.error('❌ Error creating quote:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to create quote')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Update quote
    async updateQuote(id: string, quoteData: Partial<Quote>) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await quoteApi.updateQuote(id, quoteData)
        
        // Update the quote in the list
        const index = this.quotes.findIndex(quote => quote._id === id)
        if (index !== -1) {
          this.quotes[index] = response.data
        }

        // Update current quote if it's the same
        if (this.currentQuote?._id === id) {
          this.currentQuote = response.data
        }

        console.log('✅ Quote updated successfully:', response.data.quoteNumber)
        return response.data
      } catch (error) {
        console.error('❌ Error updating quote:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to update quote')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Delete quote
    async deleteQuote(id: string) {
      try {
        this.setLoading(true)
        this.clearError()

        await quoteApi.deleteQuote(id)
        
        // Remove the quote from the list
        this.quotes = this.quotes.filter(quote => quote._id !== id)

        // Clear current quote if it's the deleted one
        if (this.currentQuote?._id === id) {
          this.currentQuote = null
        }

        console.log('✅ Quote deleted successfully')
      } catch (error) {
        console.error('❌ Error deleting quote:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to delete quote')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Fetch quote statistics
    async fetchQuoteStats() {
      try {
        this.clearError()

        const response = await quoteApi.getQuoteStats()
        
        this.stats = response.data

        console.log('✅ Quote stats fetched successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error fetching quote stats:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to fetch quote statistics')
        throw error
      }
    },

    // Convert quote to invoice
    async convertToInvoice(id: string, dueDate?: string) {
      try {
        this.setLoading(true)
        this.clearError()

        const response = await quoteApi.convertToInvoice(id, dueDate)
        
        console.log('✅ Quote converted to invoice successfully')
        return response.data
      } catch (error) {
        console.error('❌ Error converting quote to invoice:', error)
        this.setError(error instanceof Error ? error.message : 'Failed to convert quote to invoice')
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // Set current quote
    setCurrentQuote(quote: Quote | null) {
      this.currentQuote = quote
    },

    // Clear all data
    clearData() {
      this.quotes = []
      this.currentQuote = null
      this.stats = null
      this.pagination = null
      this.error = null
    },

    // Refresh quotes (re-fetch with current params)
    async refreshQuotes() {
      await this.fetchQuotes()
    }
  }
})
