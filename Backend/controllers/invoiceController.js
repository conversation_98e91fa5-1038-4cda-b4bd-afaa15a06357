const Invoice = require('../models/Invoice');
const mongoose = require('mongoose');

// Helper function to check database connection
const checkDBConnection = () => {
  return mongoose.connection.readyState === 1;
};

// @desc    Get all invoices for authenticated user with filtering, pagination, and search
// @route   GET /api/invoices
// @access  Private (Protected by Supabase JWT)
const getInvoices = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      search,
      status,
      clientName,
      invoiceType,
      startDate,
      endDate,
      sort = '-createdAt'
    } = req.query;

    // Build query object
    const query = { userId };

    // Search functionality (invoice number, client name, client email)
    if (search) {
      query.$or = [
        { invoiceNumber: { $regex: search, $options: 'i' } },
        { clientName: { $regex: search, $options: 'i' } },
        { clientEmail: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    if (status) {
      query.status = status;
    }

    // Client name filter
    if (clientName) {
      query.clientName = { $regex: clientName, $options: 'i' };
    }

    // Invoice type filter
    if (invoiceType) {
      query.invoiceType = invoiceType;
    }

    // Date range filter (based on issue date)
    if (startDate || endDate) {
      query.issueDate = {};
      if (startDate) {
        query.issueDate.$gte = new Date(startDate);
      }
      if (endDate) {
        query.issueDate.$lte = new Date(endDate);
      }
    }

    // Calculate pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const totalInvoices = await Invoice.countDocuments(query);
    const totalPages = Math.ceil(totalInvoices / limitNum);

    // Execute query with pagination and sorting
    const invoices = await Invoice.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limitNum);

    // Build pagination info
    const pagination = {
      page: pageNum,
      limit: limitNum,
      totalPages,
      totalInvoices,
      hasNextPage: pageNum < totalPages,
      hasPrevPage: pageNum > 1
    };

    res.json({
      success: true,
      count: invoices.length,
      data: invoices,
      pagination,
      filters: {
        search,
        status,
        clientName,
        invoiceType,
        startDate,
        endDate,
        sort
      }
    });
  } catch (error) {
    console.error('Get invoices error:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Get single invoice for authenticated user
// @route   GET /api/invoices/:id
// @access  Private (Protected by Supabase JWT)
const getInvoice = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const invoiceId = req.params.id;

    console.log(`🔍 Fetching invoice: ID=${invoiceId}, UserID=${userId}`);

    const invoice = await Invoice.findOne({ _id: invoiceId, userId });

    if (!invoice) {
      console.log(`❌ Invoice not found: ID=${invoiceId}, UserID=${userId}`);
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    console.log(`✅ Invoice found: ${invoice.invoiceNumber}`, {
      id: invoice._id,
      clientName: invoice.clientName,
      clientEmail: invoice.clientEmail,
      total: invoice.total,
      itemsCount: invoice.items?.length || 0,
      subtotal: invoice.subtotal,
      taxAmount: invoice.taxAmount,
      discountAmount: invoice.discountAmount
    });

    res.json({
      success: true,
      data: invoice
    });
  } catch (error) {
    console.error('❌ Error fetching invoice:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Create new invoice for authenticated user
// @route   POST /api/invoices
// @access  Private (Protected by Supabase JWT)
const createInvoice = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const invoiceData = { ...req.body, userId };
    const invoice = new Invoice(invoiceData);

    // Generate invoice number if not provided
    if (!invoice.invoiceNumber) {
      invoice.invoiceNumber = await invoice.generateInvoiceNumber();
    }

    const savedInvoice = await invoice.save();

    res.status(201).json({
      success: true,
      message: 'Invoice created successfully',
      data: savedInvoice
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation Error',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Update invoice for authenticated user
// @route   PUT /api/invoices/:id
// @access  Private (Protected by Supabase JWT)
const updateInvoice = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const invoice = await Invoice.findOneAndUpdate(
      { _id: req.params.id, userId },
      req.body,
      {
        new: true,
        runValidators: true
      }
    );

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    res.json({
      success: true,
      message: 'Invoice updated successfully',
      data: invoice
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation Error',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Delete invoice for authenticated user
// @route   DELETE /api/invoices/:id
// @access  Private (Protected by Supabase JWT)
const deleteInvoice = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const invoice = await Invoice.findOneAndDelete({ _id: req.params.id, userId });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    res.json({
      success: true,
      message: 'Invoice deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Get invoice statistics for authenticated user
// @route   GET /api/invoices/stats
// @access  Private (Protected by Supabase JWT)
const getInvoiceStats = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;

    // Get all invoices for the user
    const invoices = await Invoice.find({ userId });

    // Calculate statistics with improved logic
    const stats = {
      totalInvoices: invoices.length,
      totalAmount: invoices.reduce((sum, inv) => sum + inv.total, 0),

      // Use isPaid field for more accurate paid calculations
      paidAmount: invoices.filter(inv => inv.isPaid || inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0),

      // Pending includes sent and overdue (unpaid invoices)
      pendingAmount: invoices.filter(inv => !inv.isPaid && inv.status !== 'draft' && inv.status !== 'cancelled').reduce((sum, inv) => sum + inv.total, 0),

      // Overdue amount (unpaid and past due date)
      overdueAmount: invoices.filter(inv => {
        if (inv.isPaid || inv.status === 'draft' || inv.status === 'cancelled') return false;
        const currentDate = new Date();
        const dueDate = new Date(inv.dueDate);
        return currentDate > dueDate;
      }).reduce((sum, inv) => sum + inv.total, 0),

      // Status counts
      draftCount: invoices.filter(inv => inv.status === 'draft').length,
      sentCount: invoices.filter(inv => inv.status === 'sent').length,
      paidCount: invoices.filter(inv => inv.isPaid || inv.status === 'paid').length,
      overdueCount: invoices.filter(inv => {
        if (inv.isPaid || inv.status === 'draft' || inv.status === 'cancelled') return false;
        const currentDate = new Date();
        const dueDate = new Date(inv.dueDate);
        return currentDate > dueDate;
      }).length
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Mark invoice as paid/unpaid for authenticated user
// @route   PATCH /api/invoices/:id/payment-status
// @access  Private (Protected by Supabase JWT)
const updatePaymentStatus = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const { isPaid } = req.body;

    if (typeof isPaid !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isPaid must be a boolean value'
      });
    }

    // First, get the current invoice to check its current status and due date
    const currentInvoice = await Invoice.findOne({ _id: req.params.id, userId });

    if (!currentInvoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    // Determine the new status based on payment status and due date
    let newStatus;
    if (isPaid) {
      newStatus = 'paid';
    } else {
      // If marking as unpaid, determine status based on due date and current status
      const currentDate = new Date();
      const dueDate = new Date(currentInvoice.dueDate);

      if (currentInvoice.status === 'draft') {
        newStatus = 'draft'; // Keep as draft if it was draft
      } else if (currentDate > dueDate) {
        newStatus = 'overdue'; // Mark as overdue if past due date
      } else {
        newStatus = 'sent'; // Mark as sent if not overdue
      }
    }

    // Update both isPaid and status fields
    const invoice = await Invoice.findOneAndUpdate(
      { _id: req.params.id, userId },
      {
        isPaid,
        status: newStatus
      },
      {
        new: true,
        runValidators: true
      }
    );

    console.log(`💰 Payment status updated: Invoice ${invoice.invoiceNumber} - isPaid: ${isPaid}, status: ${newStatus}`);

    res.json({
      success: true,
      message: `Invoice marked as ${isPaid ? 'paid' : 'unpaid'} successfully`,
      data: invoice
    });
  } catch (error) {
    console.error('Payment status update error:', error);
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Generate PDF for invoice
// @route   GET /api/invoices/:id/pdf
// @access  Private (Protected by Supabase JWT)
const generateInvoicePDF = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const invoice = await Invoice.findOne({ _id: req.params.id, userId });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    // For now, return the invoice data for PDF generation on frontend
    // In production, you would use a PDF library like puppeteer or jsPDF
    res.json({
      success: true,
      message: 'Invoice data for PDF generation',
      data: invoice
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

// @desc    Send invoice via email
// @route   POST /api/invoices/:id/send-email
// @access  Private (Protected by Supabase JWT)
const sendInvoiceEmail = async (req, res) => {
  try {
    if (!checkDBConnection()) {
      return res.status(503).json({
        success: false,
        message: 'Database not connected',
        error: 'Please configure MongoDB connection string in .env file'
      });
    }

    const userId = req.user.id;
    const { subject, message, recipientEmail } = req.body;

    const invoice = await Invoice.findOne({ _id: req.params.id, userId });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    // For now, just return success
    // In production, you would integrate with an email service like SendGrid, Nodemailer, etc.
    res.json({
      success: true,
      message: 'Invoice email sent successfully',
      data: {
        invoiceId: invoice._id,
        invoiceNumber: invoice.invoiceNumber,
        recipientEmail: recipientEmail || invoice.clientEmail,
        subject: subject || `Invoice ${invoice.invoiceNumber}`,
        message: message || `Please find attached invoice ${invoice.invoiceNumber}.`
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server Error',
      error: error.message
    });
  }
};

module.exports = {
  getInvoices,
  getInvoice,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  getInvoiceStats,
  updatePaymentStatus,
  generateInvoicePDF,
  sendInvoiceEmail
};
