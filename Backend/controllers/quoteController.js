const Quote = require('../models/Quote');
const Client = require('../models/Client');
const mongoose = require('mongoose');

/**
 * @desc    Get all quotes for authenticated user
 * @route   GET /api/quotes
 * @access  Private (Protected by Supabase JWT)
 */
const getQuotes = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      search,
      status,
      sort = '-createdAt'
    } = req.query;

    const options = {
      status,
      search,
      sort,
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get quotes with pagination
    const quotes = await Quote.findByUser(userId, options);
    const totalQuotes = await Quote.countDocuments({ 
      userId,
      ...(status && status !== 'all' && { status }),
      ...(search && {
        $or: [
          { quoteNumber: { $regex: search, $options: 'i' } },
          { clientName: { $regex: search, $options: 'i' } },
          { clientEmail: { $regex: search, $options: 'i' } }
        ]
      })
    });

    const totalPages = Math.ceil(totalQuotes / parseInt(limit));

    res.status(200).json({
      success: true,
      count: quotes.length,
      totalQuotes,
      totalPages,
      currentPage: parseInt(page),
      data: quotes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
        totalQuotes,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Get quotes error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve quotes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get single quote by ID
 * @route   GET /api/quotes/:id
 * @access  Private (Protected by Supabase JWT)
 */
const getQuote = async (req, res) => {
  try {
    const userId = req.user.id;
    const quoteId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid quote ID format'
      });
    }

    const quote = await Quote.findOne({ 
      _id: quoteId, 
      userId 
    }).populate('clientId', 'name email type company address');

    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found'
      });
    }

    res.status(200).json({
      success: true,
      data: quote
    });
  } catch (error) {
    console.error('Get quote error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve quote',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new quote
 * @route   POST /api/quotes
 * @access  Private (Protected by Supabase JWT)
 */
const createQuote = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Validate client exists
    if (req.body.clientId) {
      const client = await Client.findOne({ 
        _id: req.body.clientId, 
        userId 
      });
      
      if (!client) {
        return res.status(400).json({
          success: false,
          message: 'Client not found'
        });
      }
      
      // Auto-populate client information
      req.body.clientName = client.name;
      req.body.clientEmail = client.email;
      req.body.clientAddress = client.address;
    }
    
    // Add userId to the quote data
    const quoteData = {
      ...req.body,
      userId
    };

    const quote = new Quote(quoteData);

    // Generate quote number if not provided
    if (!quote.quoteNumber) {
      quote.quoteNumber = quote.generateQuoteNumber();
    }

    const savedQuote = await quote.save();

    res.status(201).json({
      success: true,
      message: 'Quote created successfully',
      data: savedQuote
    });
  } catch (error) {
    console.error('Create quote error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Quote number already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create quote',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update quote
 * @route   PUT /api/quotes/:id
 * @access  Private (Protected by Supabase JWT)
 */
const updateQuote = async (req, res) => {
  try {
    const userId = req.user.id;
    const quoteId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid quote ID format'
      });
    }

    // Check if quote exists and belongs to user
    const existingQuote = await Quote.findOne({ 
      _id: quoteId, 
      userId 
    });

    if (!existingQuote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found'
      });
    }

    // Validate client if being updated
    if (req.body.clientId && req.body.clientId !== existingQuote.clientId.toString()) {
      const client = await Client.findOne({ 
        _id: req.body.clientId, 
        userId 
      });
      
      if (!client) {
        return res.status(400).json({
          success: false,
          message: 'Client not found'
        });
      }
      
      // Auto-populate client information
      req.body.clientName = client.name;
      req.body.clientEmail = client.email;
      req.body.clientAddress = client.address;
    }

    // Update quote
    const updatedQuote = await Quote.findByIdAndUpdate(
      quoteId,
      { ...req.body, updatedAt: new Date() },
      { 
        new: true, 
        runValidators: true 
      }
    ).populate('clientId', 'name email type');

    res.status(200).json({
      success: true,
      message: 'Quote updated successfully',
      data: updatedQuote
    });
  } catch (error) {
    console.error('Update quote error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update quote',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete quote
 * @route   DELETE /api/quotes/:id
 * @access  Private (Protected by Supabase JWT)
 */
const deleteQuote = async (req, res) => {
  try {
    const userId = req.user.id;
    const quoteId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid quote ID format'
      });
    }

    const quote = await Quote.findOneAndDelete({
      _id: quoteId,
      userId
    });

    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Quote deleted successfully',
      data: quote
    });
  } catch (error) {
    console.error('Delete quote error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete quote',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get quote statistics
 * @route   GET /api/quotes/stats
 * @access  Private (Protected by Supabase JWT)
 */
const getQuoteStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await Quote.aggregate([
      { $match: { userId } },
      {
        $group: {
          _id: null,
          totalQuotes: { $sum: 1 },
          draftQuotes: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          sentQuotes: {
            $sum: { $cond: [{ $eq: ['$status', 'sent'] }, 1, 0] }
          },
          acceptedQuotes: {
            $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, 1, 0] }
          },
          rejectedQuotes: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          expiredQuotes: {
            $sum: { $cond: [{ $eq: ['$status', 'expired'] }, 1, 0] }
          },
          totalValue: { $sum: '$total' },
          acceptedValue: {
            $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, '$total', 0] }
          },
          averageValue: { $avg: '$total' }
        }
      }
    ]);

    const result = stats[0] || {
      totalQuotes: 0,
      draftQuotes: 0,
      sentQuotes: 0,
      acceptedQuotes: 0,
      rejectedQuotes: 0,
      expiredQuotes: 0,
      totalValue: 0,
      acceptedValue: 0,
      averageValue: 0
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get quote stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve quote statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Convert quote to invoice
 * @route   POST /api/quotes/:id/convert
 * @access  Private (Protected by Supabase JWT)
 */
const convertToInvoice = async (req, res) => {
  try {
    const userId = req.user.id;
    const quoteId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(quoteId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid quote ID format'
      });
    }

    const quote = await Quote.findOne({
      _id: quoteId,
      userId
    });

    if (!quote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found'
      });
    }

    if (quote.status !== 'accepted') {
      return res.status(400).json({
        success: false,
        message: 'Only accepted quotes can be converted to invoices'
      });
    }

    // Create invoice data from quote
    const Invoice = require('../models/Invoice');
    const invoiceData = {
      userId: quote.userId,
      clientName: quote.clientName,
      clientEmail: quote.clientEmail,
      clientAddress: quote.clientAddress,
      items: quote.items,
      subtotal: quote.subtotal,
      taxRate: quote.taxRate,
      taxAmount: quote.taxAmount,
      total: quote.total,
      dueDate: req.body.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      notes: quote.notes,
      quoteId: quote._id
    };

    const invoice = new Invoice(invoiceData);

    // Generate invoice number
    if (!invoice.invoiceNumber) {
      invoice.invoiceNumber = invoice.generateInvoiceNumber();
    }

    const savedInvoice = await invoice.save();

    res.status(201).json({
      success: true,
      message: 'Quote converted to invoice successfully',
      data: {
        invoice: savedInvoice,
        quote: quote
      }
    });
  } catch (error) {
    console.error('Convert quote to invoice error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to convert quote to invoice',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getQuotes,
  getQuote,
  createQuote,
  updateQuote,
  deleteQuote,
  getQuoteStats,
  convertToInvoice
};
