const Client = require('../models/Client');
const mongoose = require('mongoose');

/**
 * @desc    Get all clients for authenticated user
 * @route   GET /api/clients
 * @access  Private (Protected by Supabase JWT)
 */
const getClients = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 10,
      search,
      status,
      sort = '-createdAt'
    } = req.query;

    const options = {
      status,
      search,
      sort,
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };

    // Get clients with pagination
    const clients = await Client.findByUser(userId, options);
    const totalClients = await Client.countDocuments({ 
      userId,
      ...(status && { status }),
      ...(search && {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { 'company.name': { $regex: search, $options: 'i' } }
        ]
      })
    });

    const totalPages = Math.ceil(totalClients / parseInt(limit));

    res.status(200).json({
      success: true,
      count: clients.length,
      totalClients,
      totalPages,
      currentPage: parseInt(page),
      data: clients,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
        totalClients,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Get clients error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve clients',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get single client by ID
 * @route   GET /api/clients/:id
 * @access  Private (Protected by Supabase JWT)
 */
const getClient = async (req, res) => {
  try {
    const userId = req.user.id;
    const clientId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID format'
      });
    }

    const client = await Client.findOne({ 
      _id: clientId, 
      userId 
    });

    if (!client) {
      return res.status(404).json({
        success: false,
        message: 'Client not found'
      });
    }

    res.status(200).json({
      success: true,
      data: client
    });
  } catch (error) {
    console.error('Get client error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve client',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new client
 * @route   POST /api/clients
 * @access  Private (Protected by Supabase JWT)
 */
const createClient = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Add userId to the client data
    const clientData = {
      ...req.body,
      userId
    };

    // Check if client with same email already exists for this user
    const existingClient = await Client.findOne({
      userId,
      email: clientData.email
    });

    if (existingClient) {
      return res.status(400).json({
        success: false,
        message: 'Client with this email already exists'
      });
    }

    const client = await Client.create(clientData);

    res.status(201).json({
      success: true,
      message: 'Client created successfully',
      data: client
    });
  } catch (error) {
    console.error('Create client error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Client with this email already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create client',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update client
 * @route   PUT /api/clients/:id
 * @access  Private (Protected by Supabase JWT)
 */
const updateClient = async (req, res) => {
  try {
    const userId = req.user.id;
    const clientId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID format'
      });
    }

    // Check if client exists and belongs to user
    const existingClient = await Client.findOne({ 
      _id: clientId, 
      userId 
    });

    if (!existingClient) {
      return res.status(404).json({
        success: false,
        message: 'Client not found'
      });
    }

    // Check for email conflicts (if email is being updated)
    if (req.body.email && req.body.email !== existingClient.email) {
      const emailConflict = await Client.findOne({
        userId,
        email: req.body.email,
        _id: { $ne: clientId }
      });

      if (emailConflict) {
        return res.status(400).json({
          success: false,
          message: 'Another client with this email already exists'
        });
      }
    }

    // Update client
    const updatedClient = await Client.findByIdAndUpdate(
      clientId,
      { ...req.body, updatedAt: new Date() },
      { 
        new: true, 
        runValidators: true 
      }
    );

    res.status(200).json({
      success: true,
      message: 'Client updated successfully',
      data: updatedClient
    });
  } catch (error) {
    console.error('Update client error:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: messages
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update client',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete client
 * @route   DELETE /api/clients/:id
 * @access  Private (Protected by Supabase JWT)
 */
const deleteClient = async (req, res) => {
  try {
    const userId = req.user.id;
    const clientId = req.params.id;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID format'
      });
    }

    const client = await Client.findOneAndDelete({ 
      _id: clientId, 
      userId 
    });

    if (!client) {
      return res.status(404).json({
        success: false,
        message: 'Client not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Client deleted successfully',
      data: client
    });
  } catch (error) {
    console.error('Delete client error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete client',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get client statistics
 * @route   GET /api/clients/stats
 * @access  Private (Protected by Supabase JWT)
 */
const getClientStats = async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await Client.aggregate([
      { $match: { userId } },
      {
        $group: {
          _id: null,
          totalClients: { $sum: 1 },
          activeClients: {
            $sum: { $cond: [{ $eq: ['$status', 'Active'] }, 1, 0] }
          },
          inactiveClients: {
            $sum: { $cond: [{ $eq: ['$status', 'Inactive'] }, 1, 0] }
          },
          totalInvoiceAmount: { $sum: '$totalAmount' },
          totalOutstanding: { $sum: '$outstandingAmount' }
        }
      }
    ]);

    const result = stats[0] || {
      totalClients: 0,
      activeClients: 0,
      inactiveClients: 0,
      totalInvoiceAmount: 0,
      totalOutstanding: 0
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get client stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve client statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get comprehensive client reports data
 * @route   GET /api/clients/reports
 * @access  Private (Protected by Supabase JWT)
 */
const getClientReports = async (req, res) => {
  try {
    const userId = req.user.id;
    const { dateRange, clientId, reportType } = req.query;

    // Calculate date filter based on dateRange
    let dateFilter = {};
    const now = new Date();

    if (dateRange === 'This Month') {
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      dateFilter = { createdAt: { $gte: startOfMonth } };
    } else if (dateRange === 'Last Month') {
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
      dateFilter = {
        createdAt: {
          $gte: startOfLastMonth,
          $lte: endOfLastMonth
        }
      };
    } else if (dateRange === 'This Year') {
      const startOfYear = new Date(now.getFullYear(), 0, 1);
      dateFilter = { createdAt: { $gte: startOfYear } };
    }

    // Get basic client stats
    const basicStats = await Client.aggregate([
      { $match: { userId, ...dateFilter } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalAmount' },
          totalInvoices: { $sum: '$totalInvoices' },
          newClients: { $sum: 1 }
        }
      }
    ]);

    // Get top clients by revenue
    const topClients = await Client.find({ userId, ...dateFilter })
      .sort({ totalAmount: -1 })
      .limit(10)
      .select('name type totalAmount totalInvoices status');

    // Get detailed client report
    let clientFilter = { userId, ...dateFilter };
    if (clientId && clientId !== 'All Clients') {
      clientFilter._id = clientId;
    }

    const detailedClients = await Client.find(clientFilter)
      .select('name email type totalInvoices totalAmount outstandingAmount status')
      .sort({ totalAmount: -1 });

    // Calculate average invoice value
    const totalRevenue = basicStats[0]?.totalRevenue || 0;
    const totalInvoices = basicStats[0]?.totalInvoices || 0;
    const avgInvoiceValue = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;

    // Get all clients for filter dropdown
    const allClients = await Client.find({ userId })
      .select('_id name')
      .sort({ name: 1 });

    const result = {
      summary: {
        totalRevenue: totalRevenue,
        totalInvoices: totalInvoices,
        avgInvoiceValue: Math.round(avgInvoiceValue * 100) / 100,
        newClients: basicStats[0]?.newClients || 0
      },
      topClients: topClients.map(client => ({
        name: client.name,
        type: client.type?.toLowerCase() || 'individual',
        revenue: client.totalAmount || 0,
        invoices: client.totalInvoices || 0
      })),
      detailedClients: detailedClients.map(client => ({
        name: client.name,
        email: client.email,
        type: client.type || 'Individual',
        invoices: client.totalInvoices || 0,
        revenue: client.totalAmount || 0,
        avgInvoice: client.totalInvoices > 0 ? Math.round((client.totalAmount / client.totalInvoices) * 100) / 100 : 0,
        status: client.status || 'Active'
      })),
      clients: allClients.map(client => ({
        _id: client._id,
        name: client.name
      }))
    };

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get client reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve client reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get comprehensive client reports data (test endpoint)
 * @route   GET /api/clients/reports/test
 * @access  Public (for testing only)
 */
const getClientReportsTest = async (req, res) => {
  try {
    // For testing, we'll create comprehensive mock data that matches real structure
    const { dateRange, clientId, reportType } = req.query;

    // Create comprehensive mock data based on filters
    let mockData = {
      // Base summary data
      summary: {
        totalRevenue: 125750,
        totalInvoices: 47,
        avgInvoiceValue: 2675.53,
        newClients: 6
      },

      // Top clients by revenue
      topClients: [
        {
          name: "Tech Solutions Ltd",
          type: "business",
          revenue: 45200,
          invoices: 12
        },
        {
          name: "John Wilson",
          type: "individual",
          revenue: 32100,
          invoices: 15
        },
        {
          name: "Acme Corporation",
          type: "business",
          revenue: 28400,
          invoices: 8
        },
        {
          name: "Marketing Inc",
          type: "business",
          revenue: 20050,
          invoices: 12
        }
      ],

      // Detailed client report
      detailedClients: [
        {
          name: "Tech Solutions Ltd",
          email: "<EMAIL>",
          type: "Business",
          invoices: 12,
          revenue: 45200,
          avgInvoice: 3766.67,
          status: "Active"
        },
        {
          name: "John Wilson",
          email: "<EMAIL>",
          type: "Individual",
          invoices: 15,
          revenue: 32100,
          avgInvoice: 2140,
          status: "Active"
        },
        {
          name: "Acme Corporation",
          email: "<EMAIL>",
          type: "Business",
          invoices: 8,
          revenue: 28400,
          avgInvoice: 3550,
          status: "Active"
        },
        {
          name: "Marketing Inc",
          email: "<EMAIL>",
          type: "Business",
          invoices: 12,
          revenue: 20050,
          avgInvoice: 1670.83,
          status: "Inactive"
        },
        {
          name: "Sarah Johnson",
          email: "<EMAIL>",
          type: "Individual",
          invoices: 3,
          revenue: 8750,
          avgInvoice: 2916.67,
          status: "Active"
        },
        {
          name: "Global Enterprises",
          email: "<EMAIL>",
          type: "Business",
          invoices: 5,
          revenue: 15200,
          avgInvoice: 3040,
          status: "Active"
        }
      ],

      // All clients for filter dropdown
      clients: [
        { _id: "674a858dcb9bcb00c9a6a61a", name: "Tech Solutions Ltd" },
        { _id: "674a858dcb9bcb00c9a6a61b", name: "John Wilson" },
        { _id: "674a858dcb9bcb00c9a6a61c", name: "Acme Corporation" },
        { _id: "674a858dcb9bcb00c9a6a61d", name: "Marketing Inc" },
        { _id: "674a858dcb9bcb00c9a6a61e", name: "Sarah Johnson" },
        { _id: "674a858dcb9bcb00c9a6a61f", name: "Global Enterprises" }
      ]
    };

    // Apply filters to modify data
    if (dateRange === 'Last Month') {
      // Reduce numbers for last month
      mockData.summary.totalRevenue = 89500;
      mockData.summary.totalInvoices = 32;
      mockData.summary.avgInvoiceValue = 2796.88;
      mockData.summary.newClients = 2;
    } else if (dateRange === 'This Year') {
      // Increase numbers for full year
      mockData.summary.totalRevenue = 485750;
      mockData.summary.totalInvoices = 156;
      mockData.summary.avgInvoiceValue = 3113.78;
      mockData.summary.newClients = 18;
    }

    // Filter by specific client if requested
    if (clientId && clientId !== 'All Clients') {
      const selectedClient = mockData.detailedClients.find(c =>
        mockData.clients.find(client => client._id === clientId && client.name === c.name)
      );

      if (selectedClient) {
        mockData.detailedClients = [selectedClient];
        mockData.topClients = mockData.topClients.filter(c => c.name === selectedClient.name);
        mockData.summary = {
          totalRevenue: selectedClient.revenue,
          totalInvoices: selectedClient.invoices,
          avgInvoiceValue: selectedClient.avgInvoice,
          newClients: 1
        };
      }
    }

    res.status(200).json({
      success: true,
      data: mockData
    });
  } catch (error) {
    console.error('Get client reports test error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve client reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Fix existing clients (temporary endpoint)
 * @route   POST /api/clients/fix-existing
 * @access  Private (Protected by Supabase JWT)
 */
const fixExistingClients = async (req, res) => {
  try {
    const userId = req.user.id;

    // Find all clients for this user
    const clients = await Client.find({ userId });

    let updatedCount = 0;

    for (const client of clients) {
      let needsUpdate = false;

      // Fix missing clientId
      if (!client.clientId) {
        needsUpdate = true;
      }

      // Fix missing name (if it's empty but has email)
      if (!client.name && client.email) {
        client.name = client.email.split('@')[0]; // Use email prefix as name
        needsUpdate = true;
      }

      if (needsUpdate) {
        await client.save(); // This will trigger the pre-save middleware
        updatedCount++;
      }
    }

    res.status(200).json({
      success: true,
      message: `Fixed ${updatedCount} clients`,
      data: { updatedCount, totalClients: clients.length }
    });
  } catch (error) {
    console.error('Fix existing clients error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fix existing clients',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getClients,
  getClient,
  createClient,
  updateClient,
  deleteClient,
  getClientStats,
  getClientReports,
  getClientReportsTest,
  fixExistingClients
};
