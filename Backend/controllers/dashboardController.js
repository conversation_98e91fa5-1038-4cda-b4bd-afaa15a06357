const Client = require('../models/Client');
const Invoice = require('../models/Invoice');
const Quote = require('../models/Quote');
const Expense = require('../models/Expense');
const Product = require('../models/Product');
const Service = require('../models/Service');

/**
 * @desc    Get dashboard overview statistics
 * @route   GET /api/dashboard/overview
 * @access  Private (Protected by Supabase JWT)
 */
const getDashboardOverview = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get all statistics in parallel
    const [
      clientStats,
      invoiceStats,
      quoteStats,
      expenseStats,
      productStats,
      serviceStats
    ] = await Promise.all([
      // Client statistics
      Client.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalClients: { $sum: 1 },
            activeClients: {
              $sum: { $cond: [{ $eq: ['$status', 'Active'] }, 1, 0] }
            }
          }
        }
      ]),

      // Invoice statistics
      Invoice.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalInvoices: { $sum: 1 },
            totalRevenue: { $sum: '$total' },
            paidInvoices: {
              $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] }
            },
            paidRevenue: {
              $sum: { $cond: [{ $eq: ['$status', 'paid'] }, '$total', 0] }
            },
            pendingRevenue: {
              $sum: { $cond: [{ $ne: ['$status', 'paid'] }, '$total', 0] }
            }
          }
        }
      ]),

      // Quote statistics
      Quote.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalQuotes: { $sum: 1 },
            acceptedQuotes: {
              $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, 1, 0] }
            },
            totalQuoteValue: { $sum: '$total' },
            acceptedQuoteValue: {
              $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, '$total', 0] }
            }
          }
        }
      ]),

      // Expense statistics
      Expense.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalExpenses: { $sum: '$totalAmount' },
            pendingExpenses: {
              $sum: { $cond: [{ $eq: ['$status', 'pending'] }, '$totalAmount', 0] }
            },
            approvedExpenses: {
              $sum: { $cond: [{ $eq: ['$status', 'approved'] }, '$totalAmount', 0] }
            }
          }
        }
      ]),

      // Product statistics
      Product.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalProducts: { $sum: 1 },
            activeProducts: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            totalProductValue: { $sum: { $multiply: ['$stock', '$costPrice'] } },
            lowStockProducts: {
              $sum: { $cond: [{ $lte: ['$stock', '$minStockLevel'] }, 1, 0] }
            }
          }
        }
      ]),

      // Service statistics
      Service.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalServices: { $sum: 1 },
            activeServices: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            totalServiceRevenue: { $sum: '$totalRevenue' }
          }
        }
      ])
    ]);

    // Format the results
    const overview = {
      clients: {
        total: clientStats[0]?.totalClients || 0,
        active: clientStats[0]?.activeClients || 0
      },
      invoices: {
        total: invoiceStats[0]?.totalInvoices || 0,
        paid: invoiceStats[0]?.paidInvoices || 0,
        totalRevenue: invoiceStats[0]?.totalRevenue || 0,
        paidRevenue: invoiceStats[0]?.paidRevenue || 0,
        pendingRevenue: invoiceStats[0]?.pendingRevenue || 0
      },
      quotes: {
        total: quoteStats[0]?.totalQuotes || 0,
        accepted: quoteStats[0]?.acceptedQuotes || 0,
        totalValue: quoteStats[0]?.totalQuoteValue || 0,
        acceptedValue: quoteStats[0]?.acceptedQuoteValue || 0
      },
      expenses: {
        total: expenseStats[0]?.totalExpenses || 0,
        pending: expenseStats[0]?.pendingExpenses || 0,
        approved: expenseStats[0]?.approvedExpenses || 0
      },
      products: {
        total: productStats[0]?.totalProducts || 0,
        active: productStats[0]?.activeProducts || 0,
        totalValue: productStats[0]?.totalProductValue || 0,
        lowStock: productStats[0]?.lowStockProducts || 0
      },
      services: {
        total: serviceStats[0]?.totalServices || 0,
        active: serviceStats[0]?.activeServices || 0,
        totalRevenue: serviceStats[0]?.totalServiceRevenue || 0
      }
    };

    // Calculate derived metrics
    overview.financial = {
      totalRevenue: overview.invoices.totalRevenue + overview.services.totalRevenue,
      netIncome: overview.invoices.paidRevenue - overview.expenses.approved,
      pendingPayments: overview.invoices.pendingRevenue,
      totalExpenses: overview.expenses.total
    };

    res.status(200).json({
      success: true,
      data: overview
    });
  } catch (error) {
    console.error('Get dashboard overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard overview',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get recent activities
 * @route   GET /api/dashboard/activities
 * @access  Private (Protected by Supabase JWT)
 */
const getRecentActivities = async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 10;

    // Get recent activities from different modules
    const [recentInvoices, recentQuotes, recentExpenses, recentClients] = await Promise.all([
      Invoice.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('invoiceNumber clientName total status createdAt'),
      
      Quote.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('quoteNumber clientName total status createdAt'),
      
      Expense.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('expenseId vendor totalAmount status createdAt'),
      
      Client.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('name email type status createdAt')
    ]);

    // Combine and format activities
    const activities = [];

    recentInvoices.forEach(invoice => {
      activities.push({
        type: 'invoice',
        id: invoice._id,
        title: `Invoice ${invoice.invoiceNumber}`,
        description: `${invoice.clientName} - $${invoice.total}`,
        status: invoice.status,
        date: invoice.createdAt
      });
    });

    recentQuotes.forEach(quote => {
      activities.push({
        type: 'quote',
        id: quote._id,
        title: `Quote ${quote.quoteNumber}`,
        description: `${quote.clientName} - $${quote.total}`,
        status: quote.status,
        date: quote.createdAt
      });
    });

    recentExpenses.forEach(expense => {
      activities.push({
        type: 'expense',
        id: expense._id,
        title: `Expense ${expense.expenseId}`,
        description: `${expense.vendor} - $${expense.totalAmount}`,
        status: expense.status,
        date: expense.createdAt
      });
    });

    recentClients.forEach(client => {
      activities.push({
        type: 'client',
        id: client._id,
        title: `New Client: ${client.name}`,
        description: `${client.type} - ${client.email}`,
        status: client.status,
        date: client.createdAt
      });
    });

    // Sort by date and limit
    activities.sort((a, b) => new Date(b.date) - new Date(a.date));
    const limitedActivities = activities.slice(0, limit);

    res.status(200).json({
      success: true,
      data: limitedActivities
    });
  } catch (error) {
    console.error('Get recent activities error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent activities',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getDashboardOverview,
  getRecentActivities
};
