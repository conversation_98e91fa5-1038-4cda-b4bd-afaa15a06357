const express = require('express');
const router = express.Router();
const {
  getExpenses,
  getExpense,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStats,
  getExpenseCategories,
  approveExpense,
  rejectExpense
} = require('../controllers/expenseController');

// Import Supabase authentication middleware
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply Supabase JWT authentication to all expense routes
router.use(verifySupabaseToken);

/**
 * @route   GET /api/expenses/stats
 * @desc    Get expense statistics for authenticated user
 * @access  Private (Supabase JWT required)
 */
router.get('/stats', getExpenseStats);

/**
 * @route   GET /api/expenses/categories
 * @desc    Get expense categories
 * @access  Private (Supabase JWT required)
 */
router.get('/categories', getExpenseCategories);

/**
 * @route   GET /api/expenses
 * @desc    Get all expenses for authenticated user
 * @access  Private (Supabase JWT required)
 * @query   page, limit, search, status, category, sort, startDate, endDate
 */
router.get('/', getExpenses);

/**
 * @route   GET /api/expenses/:id
 * @desc    Get single expense by ID
 * @access  Private (Supabase JWT required)
 */
router.get('/:id', getExpense);

/**
 * @route   POST /api/expenses
 * @desc    Create new expense
 * @access  Private (Supabase JWT required)
 */
router.post('/', createExpense);

/**
 * @route   PUT /api/expenses/:id
 * @desc    Update expense by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id', updateExpense);

/**
 * @route   PUT /api/expenses/:id/approve
 * @desc    Approve expense by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id/approve', approveExpense);

/**
 * @route   PUT /api/expenses/:id/reject
 * @desc    Reject expense by ID
 * @access  Private (Supabase JWT required)
 */
router.put('/:id/reject', rejectExpense);

/**
 * @route   DELETE /api/expenses/:id
 * @desc    Delete expense by ID
 * @access  Private (Supabase JWT required)
 */
router.delete('/:id', deleteExpense);

module.exports = router;
