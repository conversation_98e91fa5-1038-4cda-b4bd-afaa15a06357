const express = require('express');
const router = express.Router();
const { verifySupabaseToken, optionalAuth } = require('../middleware/supabaseAuth');

/**
 * @route   GET /api/auth-test/public
 * @desc    Public route - no authentication required
 * @access  Public
 */
router.get('/public', (req, res) => {
  res.json({
    success: true,
    message: 'This is a public route - no authentication required',
    timestamp: new Date().toISOString()
  });
});

/**
 * @route   GET /api/auth-test/optional
 * @desc    Optional authentication - works with or without token
 * @access  Public/Private
 */
router.get('/optional', optionalAuth, (req, res) => {
  res.json({
    success: true,
    message: 'This route has optional authentication',
    authenticated: !!req.user,
    user: req.user ? {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role
    } : null,
    timestamp: new Date().toISOString()
  });
});

/**
 * @route   GET /api/auth-test/protected
 * @desc    Protected route - requires valid Supabase JWT
 * @access  Private
 */
router.get('/protected', verifySupabaseToken, (req, res) => {
  res.json({
    success: true,
    message: 'This is a protected route - authentication required',
    user: {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role,
      metadata: req.user.metadata,
      tokenInfo: {
        issued: new Date(req.user.iat * 1000).toISOString(),
        expires: new Date(req.user.exp * 1000).toISOString()
      }
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * @route   GET /api/auth-test/debug-token
 * @desc    Debug JWT token structure - shows token details
 * @access  Public (for debugging)
 */
router.get('/debug-token', (req, res) => {
  const jwt = require('jsonwebtoken');

  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.json({
        success: false,
        message: 'No Authorization header provided',
        help: 'Send Authorization: Bearer <token> header'
      });
    }

    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

    if (!token) {
      return res.json({
        success: false,
        message: 'No token found in Authorization header'
      });
    }

    // Decode token without verification to inspect structure
    const decoded = jwt.decode(token, { complete: true });

    if (!decoded) {
      return res.json({
        success: false,
        message: 'Could not decode token'
      });
    }

    res.json({
      success: true,
      message: 'JWT Token Debug Information',
      tokenStructure: {
        header: decoded.header,
        payload: {
          iss: decoded.payload.iss,
          aud: decoded.payload.aud,
          exp: decoded.payload.exp,
          iat: decoded.payload.iat,
          sub: decoded.payload.sub,
          email: decoded.payload.email,
          role: decoded.payload.role,
          user_metadata: decoded.payload.user_metadata,
          app_metadata: decoded.payload.app_metadata
        }
      },
      tokenTiming: {
        issued: new Date(decoded.payload.iat * 1000).toISOString(),
        expires: new Date(decoded.payload.exp * 1000).toISOString(),
        isExpired: decoded.payload.exp < Math.floor(Date.now() / 1000)
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.json({
      success: false,
      message: 'Error debugging token',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/auth-test/echo
 * @desc    Echo back request data - requires authentication
 * @access  Private
 */
router.post('/echo', verifySupabaseToken, (req, res) => {
  res.json({
    success: true,
    message: 'Echo endpoint - returns your request data',
    user: {
      id: req.user.id,
      email: req.user.email
    },
    requestData: {
      body: req.body,
      query: req.query,
      headers: {
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent']
      }
    },
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
