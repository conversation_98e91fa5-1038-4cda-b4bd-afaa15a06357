const express = require('express');
const router = express.Router();
const {
  getClients,
  getClient,
  createClient,
  updateClient,
  deleteClient,
  getClientStats,
  fixExistingClients
} = require('../controllers/clientController');

// Import Supabase authentication middleware
const { verifySupabaseToken, verifyRole } = require('../middleware/supabaseAuth');

// Import enhanced data isolation middleware
const { dataIsolationSuite } = require('../middleware/dataIsolation');
const Client = require('../models/Client');

/**
 * @route   GET /api/clients/reports/test
 * @desc    Get comprehensive client reports data (test endpoint - no auth)
 * @access  Public (for testing only)
 */
router.get('/reports/test', require('../controllers/clientController').getClientReportsTest);

// Apply Supabase JWT authentication to all other client routes
router.use(verifySupabaseToken);

// Create enhanced data isolation middleware suite for Client model
const clientSecurity = dataIsolationSuite(Client, {
  resourceType: 'client',
  enableAudit: true
});

/**
 * @route   GET /api/clients/stats
 * @desc    Get client statistics for authenticated user
 * @access  Private (Supabase JWT required + Data Isolation)
 */
router.get('/stats', clientSecurity.listProtection, getClientStats);

/**
 * @route   GET /api/clients/reports
 * @desc    Get comprehensive client reports data
 * @access  Private (Supabase JWT required + Data Isolation)
 */
router.get('/reports', clientSecurity.listProtection, require('../controllers/clientController').getClientReports);

/**
 * @route   GET /api/clients
 * @desc    Get all clients for authenticated user
 * @access  Private (Supabase JWT required + Data Isolation)
 * @query   page, limit, search, status, sort
 */
router.get('/', clientSecurity.listProtection, getClients);

/**
 * @route   GET /api/clients/:id
 * @desc    Get single client by ID
 * @access  Private (Supabase JWT required + Ownership Verification)
 */
router.get('/:id', clientSecurity.ensureOwnership, getClient);

/**
 * @route   POST /api/clients
 * @desc    Create new client
 * @access  Private (Supabase JWT required + Auto UserId Assignment)
 */
router.post('/', clientSecurity.createProtection, createClient);

/**
 * @route   POST /api/clients/fix-existing
 * @desc    Fix existing clients (temporary endpoint)
 * @access  Private (Supabase JWT required)
 */
router.post('/fix-existing', fixExistingClients);

/**
 * @route   PUT /api/clients/:id
 * @desc    Update client by ID
 * @access  Private (Supabase JWT required + Ownership Verification)
 */
router.put('/:id', clientSecurity.updateProtection, updateClient);

/**
 * @route   DELETE /api/clients/:id
 * @desc    Delete client by ID
 * @access  Private (Supabase JWT required + Ownership Verification)
 */
router.delete('/:id', clientSecurity.deleteProtection, deleteClient);

module.exports = router;
