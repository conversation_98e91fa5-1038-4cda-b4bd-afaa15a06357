const express = require('express');
const router = express.Router();
const {
  getDashboardOverview,
  getRecentActivities
} = require('../controllers/dashboardController');

// Import Supabase authentication middleware
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply Supabase JWT authentication to all dashboard routes
router.use(verifySupabaseToken);

/**
 * @route   GET /api/dashboard/overview
 * @desc    Get dashboard overview statistics
 * @access  Private (Supabase JWT required)
 */
router.get('/overview', getDashboardOverview);

/**
 * @route   GET /api/dashboard/activities
 * @desc    Get recent activities
 * @access  Private (Supabase JWT required)
 * @query   limit
 */
router.get('/activities', getRecentActivities);

module.exports = router;
