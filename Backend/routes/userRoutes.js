const express = require('express');
const router = express.Router();
const {
  getUserProfile,
  updateUserProfile,
  updatePassword,
  getUserSettings,
  updateUserSettings
} = require('../controllers/userController');
const { verifySupabaseToken } = require('../middleware/supabaseAuth');

// Apply authentication middleware to all routes
router.use(verifySupabaseToken);

// Profile routes
router.get('/profile', getUserProfile);
router.put('/profile', updateUserProfile);
router.put('/password', updatePassword);

// Settings routes
router.get('/settings', getUserSettings);
router.put('/settings', updateUserSettings);

module.exports = router;
