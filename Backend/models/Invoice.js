const mongoose = require('mongoose');

const invoiceItemSchema = new mongoose.Schema({
  description: {
    type: String,
    required: [true, 'Item description is required'],
    trim: true
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1']
  },
  unitPrice: {
    type: Number,
    required: [true, 'Unit price is required'],
    min: [0, 'Unit price cannot be negative']
  },
  total: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    enum: ['product', 'service'],
    default: 'product'
  },
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: false
  },
  serviceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: false
  }
});

const invoiceSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    index: true
  },
  invoiceNumber: {
    type: String,
    required: [true, 'Invoice number is required'],
    trim: true
  },
  clientName: {
    type: String,
    required: [true, 'Client name is required'],
    trim: true
  },
  clientEmail: {
    type: String,
    required: [true, 'Client email is required'],
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  clientPhone: {
    type: String,
    trim: true
  },
  clientAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  items: [invoiceItemSchema],
  subtotal: {
    type: Number,
    min: 0,
    default: 0
  },
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  discountRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  discountAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  total: {
    type: Number,
    min: 0,
    default: 0
  },
  invoiceType: {
    type: String,
    enum: ['product', 'service', 'mixed'],
    default: 'product'
  },
  paymentTerms: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['draft', 'sent', 'paid', 'overdue', 'cancelled'],
    default: 'draft'
  },
  isPaid: {
    type: Boolean,
    default: false
  },
  issueDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: [true, 'Due date is required']
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Create compound index to ensure invoice numbers are unique per user
invoiceSchema.index({ userId: 1, invoiceNumber: 1 }, { unique: true });

// Pre-save middleware to calculate totals
invoiceSchema.pre('save', function(next) {
  // Calculate item totals
  this.items.forEach(item => {
    item.total = item.quantity * item.unitPrice;
  });

  // Calculate subtotal
  this.subtotal = this.items.reduce((sum, item) => sum + item.total, 0);

  // Calculate discount amount
  this.discountAmount = (this.subtotal * this.discountRate) / 100;

  // Calculate tax amount (after discount)
  const taxableAmount = this.subtotal - this.discountAmount;
  this.taxAmount = (taxableAmount * this.taxRate) / 100;

  // Calculate total
  this.total = this.subtotal - this.discountAmount + this.taxAmount;

  // Determine invoice type based on items
  const hasProducts = this.items.some(item => item.type === 'product');
  const hasServices = this.items.some(item => item.type === 'service');

  if (hasProducts && hasServices) {
    this.invoiceType = 'mixed';
  } else if (hasServices) {
    this.invoiceType = 'service';
  } else {
    this.invoiceType = 'product';
  }

  // Update status based on payment and due date
  if (this.isPaid) {
    this.status = 'paid';
  } else if (this.status !== 'draft' && this.status !== 'cancelled') {
    const currentDate = new Date();
    const dueDate = new Date(this.dueDate);

    if (currentDate > dueDate) {
      this.status = 'overdue';
    } else {
      // If not overdue and not paid, set to sent (not pending)
      this.status = 'sent';
    }
  }

  next();
});

// Instance method to generate user-specific invoice number
invoiceSchema.methods.generateInvoiceNumber = async function() {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');

  // Get the count of invoices for this user in the current month
  const startOfMonth = new Date(year, date.getMonth(), 1);
  const endOfMonth = new Date(year, date.getMonth() + 1, 0);

  const count = await this.constructor.countDocuments({
    userId: this.userId,
    createdAt: {
      $gte: startOfMonth,
      $lte: endOfMonth
    }
  });

  // Generate sequential number for this user (starting from 1)
  const sequentialNumber = String(count + 1).padStart(3, '0');

  return `INV-${year}${month}-${sequentialNumber}`;
};

module.exports = mongoose.model('Invoice', invoiceSchema);
