const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import the fixed authentication middleware
const { verifySupabaseToken } = require('./middleware/supabaseAuth');

const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// Simple logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Minimal Backend Server for JWT Testing',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    jwtSecretConfigured: !!process.env.SUPABASE_JWT_SECRET
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Server is healthy',
    timestamp: new Date().toISOString()
  });
});

// Debug endpoint to inspect JWT tokens
app.get('/api/debug-token', (req, res) => {
  const jwt = require('jsonwebtoken');
  
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.json({
        success: false,
        message: 'No Authorization header provided',
        help: 'Send Authorization: Bearer <token> header'
      });
    }

    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;
    
    if (!token) {
      return res.json({
        success: false,
        message: 'No token found in Authorization header'
      });
    }

    console.log('🔍 Debugging token:', token.substring(0, 50) + '...');

    // Decode token without verification to inspect structure
    const decoded = jwt.decode(token, { complete: true });
    
    if (!decoded) {
      return res.json({
        success: false,
        message: 'Could not decode token'
      });
    }

    res.json({
      success: true,
      message: 'JWT Token Debug Information',
      tokenStructure: {
        header: decoded.header,
        payload: {
          iss: decoded.payload.iss,
          aud: decoded.payload.aud,
          exp: decoded.payload.exp,
          iat: decoded.payload.iat,
          sub: decoded.payload.sub,
          email: decoded.payload.email,
          role: decoded.payload.role,
          user_metadata: decoded.payload.user_metadata,
          app_metadata: decoded.payload.app_metadata
        }
      },
      tokenTiming: {
        issued: new Date(decoded.payload.iat * 1000).toISOString(),
        expires: new Date(decoded.payload.exp * 1000).toISOString(),
        isExpired: decoded.payload.exp < Math.floor(Date.now() / 1000),
        timeUntilExpiry: Math.max(0, decoded.payload.exp - Math.floor(Date.now() / 1000))
      },
      jwtSecret: {
        configured: !!process.env.SUPABASE_JWT_SECRET,
        length: process.env.SUPABASE_JWT_SECRET ? process.env.SUPABASE_JWT_SECRET.length : 0
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Token debug error:', error);
    res.json({
      success: false,
      message: 'Error debugging token',
      error: error.message
    });
  }
});

// Protected endpoint using the authentication middleware
app.get('/api/auth-test/protected', verifySupabaseToken, (req, res) => {
  res.json({
    success: true,
    message: 'Authentication successful! You have access to protected routes.',
    user: {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role,
      metadata: req.user.metadata
    },
    tokenInfo: {
      issued: new Date(req.user.iat * 1000).toISOString(),
      expires: new Date(req.user.exp * 1000).toISOString(),
      issuer: req.user.iss,
      audience: req.user.aud
    },
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 Minimal server running on port ${PORT}`);
  console.log(`📍 Access at: http://localhost:${PORT}`);
  console.log(`🔑 JWT Secret configured: ${!!process.env.SUPABASE_JWT_SECRET}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  if (process.env.SUPABASE_JWT_SECRET) {
    console.log(`🔐 JWT Secret length: ${process.env.SUPABASE_JWT_SECRET.length} characters`);
  } else {
    console.log('⚠️  No JWT Secret configured - check your .env file');
  }
});
