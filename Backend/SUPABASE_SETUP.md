# Supabase JWT Configuration Guide

## 🔑 Getting Your Supabase JWT Secret

To properly configure authentication between your frontend and backend, you need to get the correct JWT secret from your Supabase project.

### Step 1: Access Your Supabase Dashboard
1. Go to [https://supabase.com](https://supabase.com)
2. Sign in to your account
3. Select your project: `szzsqpwwwkvsyyednaxm`

### Step 2: Get the JWT Secret
1. In your Supabase dashboard, go to **Settings** (gear icon in sidebar)
2. Click on **API** in the settings menu
3. Scroll down to find the **JWT Secret** section
4. Copy the JWT Secret (NOT the anon key or service role key)

### Step 3: Update Backend Configuration
1. Open `Backend/.env` file
2. Replace the placeholder with your actual JWT secret:
   ```env
   SUPABASE_JWT_SECRET=your-actual-jwt-secret-here
   ```

### Step 4: Restart Your Backend Server
```bash
cd Backend
npm run dev
```

## 🔍 Troubleshooting

### Issue: "JWT_SECRET_MISSING" Error
- Make sure you've added the JWT secret to your `.env` file
- Restart your backend server after making changes

### Issue: "Invalid token" Error
- Verify you're using the JWT Secret, not the anon key
- Check that the secret is correctly copied without extra spaces

### Issue: Name Not Showing
- The user name comes from Supabase user metadata
- If users signed up with email/password, they might not have a name set
- The system will fall back to using the email username part

## 📝 User Metadata Structure

Supabase stores user information in different metadata fields:
- `user_metadata.full_name`
- `user_metadata.name`
- `user_metadata.first_name` + `user_metadata.last_name`
- `app_metadata.full_name`

The auth store will try all these fields and fall back to the email username if none are available.

## 🚀 Testing

After configuration:
1. Go to `/api-test` page in your frontend
2. Click "Test Backend Auth" - should show success
3. Try creating a test client - should work without connection errors
4. Check browser console for debug logs showing user metadata
