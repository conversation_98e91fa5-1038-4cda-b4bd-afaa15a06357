const mongoose = require('mongoose');
const Client = require('../models/Client');
const Quote = require('../models/Quote');
const Expense = require('../models/Expense');
const Product = require('../models/Product');
const Service = require('../models/Service');

// Test data isolation between users
async function testDataIsolation() {
  try {
    console.log('🧪 Testing Data Isolation Between Users...\n');

    // Connect to database
    await mongoose.connect('mongodb://localhost:27017/invoice-easy');
    console.log('✅ Connected to MongoDB\n');

    // Test User IDs (simulating different authenticated users)
    const userA = 'user-nawin-123';
    const userB = 'user-john-456';

    console.log(`👤 User A: ${userA}`);
    console.log(`👤 User B: ${userB}\n`);

    // Clean up any existing test data
    await Client.deleteMany({ userId: { $in: [userA, userB] } });
    await Quote.deleteMany({ userId: { $in: [userA, userB] } });
    await Expense.deleteMany({ userId: { $in: [userA, userB] } });
    await Product.deleteMany({ userId: { $in: [userA, userB] } });
    await Service.deleteMany({ userId: { $in: [userA, userB] } });

    console.log('🧹 Cleaned up existing test data\n');

    // Test 1: Create clients for both users
    console.log('📝 Test 1: Creating clients for both users...');
    
    const clientA1 = await Client.create({
      userId: userA,
      name: 'Nawin Client 1',
      email: '<EMAIL>',
      type: 'Individual'
    });

    const clientA2 = await Client.create({
      userId: userA,
      name: 'Nawin Client 2',
      email: '<EMAIL>',
      type: 'Business'
    });

    const clientB1 = await Client.create({
      userId: userB,
      name: 'John Client 1',
      email: '<EMAIL>',
      type: 'Individual'
    });

    const clientB2 = await Client.create({
      userId: userB,
      name: 'John Client 2',
      email: '<EMAIL>',
      type: 'Business'
    });

    console.log(`✅ Created 2 clients for User A`);
    console.log(`✅ Created 2 clients for User B\n`);

    // Test 2: Verify data isolation - each user should only see their own clients
    console.log('🔍 Test 2: Verifying client data isolation...');
    
    const userAClients = await Client.find({ userId: userA });
    const userBClients = await Client.find({ userId: userB });

    console.log(`User A can see ${userAClients.length} clients:`);
    userAClients.forEach(client => {
      console.log(`  - ${client.name} (${client.email})`);
    });

    console.log(`User B can see ${userBClients.length} clients:`);
    userBClients.forEach(client => {
      console.log(`  - ${client.name} (${client.email})`);
    });

    if (userAClients.length === 2 && userBClients.length === 2) {
      console.log('✅ Client data isolation working correctly\n');
    } else {
      console.log('❌ Client data isolation FAILED\n');
      return;
    }

    // Test 3: Create quotes for both users
    console.log('📝 Test 3: Creating quotes for both users...');
    
    await Quote.create({
      userId: userA,
      clientId: clientA1._id,
      clientName: clientA1.name,
      clientEmail: clientA1.email,
      quoteNumber: 'QT-A-001',
      items: [{ description: 'Service A', quantity: 1, unitPrice: 100, total: 100 }],
      subtotal: 100,
      total: 100,
      status: 'draft'
    });

    await Quote.create({
      userId: userB,
      clientId: clientB1._id,
      clientName: clientB1.name,
      clientEmail: clientB1.email,
      quoteNumber: 'QT-B-001',
      items: [{ description: 'Service B', quantity: 1, unitPrice: 200, total: 200 }],
      subtotal: 200,
      total: 200,
      status: 'sent'
    });

    console.log(`✅ Created quotes for both users\n`);

    // Test 4: Verify quote data isolation
    console.log('🔍 Test 4: Verifying quote data isolation...');
    
    const userAQuotes = await Quote.find({ userId: userA });
    const userBQuotes = await Quote.find({ userId: userB });

    console.log(`User A can see ${userAQuotes.length} quotes`);
    console.log(`User B can see ${userBQuotes.length} quotes`);

    if (userAQuotes.length === 1 && userBQuotes.length === 1) {
      console.log('✅ Quote data isolation working correctly\n');
    } else {
      console.log('❌ Quote data isolation FAILED\n');
      return;
    }

    // Test 5: Create expenses for both users
    console.log('📝 Test 5: Creating expenses for both users...');
    
    await Expense.create({
      userId: userA,
      vendor: 'Office Supplies A',
      category: 'Office Supplies',
      amount: 50,
      totalAmount: 50,
      description: 'User A expense',
      status: 'pending'
    });

    await Expense.create({
      userId: userB,
      vendor: 'Office Supplies B',
      category: 'Office Supplies',
      amount: 75,
      totalAmount: 75,
      description: 'User B expense',
      status: 'approved'
    });

    console.log(`✅ Created expenses for both users\n`);

    // Test 6: Verify expense data isolation
    console.log('🔍 Test 6: Verifying expense data isolation...');
    
    const userAExpenses = await Expense.find({ userId: userA });
    const userBExpenses = await Expense.find({ userId: userB });

    console.log(`User A can see ${userAExpenses.length} expenses`);
    console.log(`User B can see ${userBExpenses.length} expenses`);

    if (userAExpenses.length === 1 && userBExpenses.length === 1) {
      console.log('✅ Expense data isolation working correctly\n');
    } else {
      console.log('❌ Expense data isolation FAILED\n');
      return;
    }

    // Test 7: Test cross-user access prevention
    console.log('🔍 Test 7: Testing cross-user access prevention...');
    
    // Try to access User A's client using User B's context
    const crossAccessAttempt = await Client.findOne({ 
      _id: clientA1._id, 
      userId: userB 
    });

    if (!crossAccessAttempt) {
      console.log('✅ Cross-user access properly blocked\n');
    } else {
      console.log('❌ Cross-user access prevention FAILED\n');
      return;
    }

    // Test 8: Test unique constraints per user
    console.log('🔍 Test 8: Testing unique constraints per user...');
    
    try {
      // This should succeed - same email but different users
      await Client.create({
        userId: userB,
        name: 'Different User Same Email',
        email: '<EMAIL>', // Same email as User A's client
        type: 'Individual'
      });
      console.log('✅ Same email allowed for different users\n');
    } catch (error) {
      console.log('❌ Unique constraint test FAILED:', error.message, '\n');
    }

    try {
      // This should fail - same email and same user
      await Client.create({
        userId: userA,
        name: 'Same User Same Email',
        email: '<EMAIL>', // Same email as existing User A client
        type: 'Individual'
      });
      console.log('❌ Duplicate email prevention FAILED\n');
    } catch (error) {
      console.log('✅ Duplicate email properly prevented for same user\n');
    }

    // Final summary
    console.log('🎉 DATA ISOLATION TEST COMPLETED SUCCESSFULLY!');
    console.log('✅ All user data is properly isolated');
    console.log('✅ Cross-user access is prevented');
    console.log('✅ Unique constraints work per user');
    console.log('✅ Your application is secure!\n');

    // Clean up test data
    await Client.deleteMany({ userId: { $in: [userA, userB] } });
    await Quote.deleteMany({ userId: { $in: [userA, userB] } });
    await Expense.deleteMany({ userId: { $in: [userA, userB] } });
    console.log('🧹 Cleaned up test data');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  testDataIsolation();
}

module.exports = testDataIsolation;
