const jwt = require('jsonwebtoken');

// Supabase JWT Secret from environment variables
// This should be your Supabase project's JWT secret from Settings > API
const SUPABASE_JWT_SECRET = process.env.SUPABASE_JWT_SECRET;

// For development, we'll use a more flexible approach
// In production, you should get the actual JWT secret from Supabase dashboard
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Middleware to verify Supabase JWT tokens
 * Protects routes by validating the Authorization header
 */
const verifySupabaseToken = (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.',
        code: 'NO_TOKEN'
      });
    }

    // Extract token from "Bearer <token>" format
    const token = authHeader.startsWith('Bearer ')
      ? authHeader.slice(7)
      : authHeader;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token format.',
        code: 'INVALID_TOKEN_FORMAT'
      });
    }

    let decoded;

    // Always try to verify with JWT secret first
    if (SUPABASE_JWT_SECRET && SUPABASE_JWT_SECRET !== 'your-supabase-jwt-secret-here' && SUPABASE_JWT_SECRET !== 'super-secret-jwt-token-with-at-least-32-characters-long') {
      try {
        // First, let's decode the token to see its structure for debugging
        const decodedForDebug = jwt.decode(token, { complete: true });
        console.log('🔍 JWT Token Debug Info:', {
          header: decodedForDebug?.header,
          payload: {
            iss: decodedForDebug?.payload?.iss,
            aud: decodedForDebug?.payload?.aud,
            exp: decodedForDebug?.payload?.exp,
            sub: decodedForDebug?.payload?.sub,
            email: decodedForDebug?.payload?.email
          }
        });

        // Try verification without strict issuer/audience validation first
        decoded = jwt.verify(token, SUPABASE_JWT_SECRET, {
          algorithms: ['HS256'], // Supabase uses HS256
          // Don't verify issuer/audience as they vary by Supabase project
          ignoreExpiration: false,
          clockTolerance: 60 // Allow 60 seconds clock skew
        });
        console.log('✅ JWT verified successfully with secret');
      } catch (verifyError) {
        console.error('❌ JWT Verification failed:', verifyError.message);
        console.error('❌ JWT Verification error details:', {
          name: verifyError.name,
          message: verifyError.message,
          tokenPreview: token.substring(0, 50) + '...'
        });

        // Handle specific JWT errors with more detail
        if (verifyError.name === 'TokenExpiredError') {
          return res.status(401).json({
            success: false,
            message: 'Token has expired.',
            code: 'TOKEN_EXPIRED',
            debug: isDevelopment ? verifyError.message : undefined
          });
        }

        if (verifyError.name === 'JsonWebTokenError') {
          return res.status(401).json({
            success: false,
            message: 'Invalid token format or signature.',
            code: 'INVALID_TOKEN',
            debug: isDevelopment ? verifyError.message : undefined
          });
        }

        if (verifyError.name === 'NotBeforeError') {
          return res.status(401).json({
            success: false,
            message: 'Token not active yet.',
            code: 'TOKEN_NOT_ACTIVE',
            debug: isDevelopment ? verifyError.message : undefined
          });
        }

        // In development mode, fall back to decode without verification
        if (isDevelopment) {
          console.warn('⚠️  JWT verification failed, falling back to decode-only mode for development');
          try {
            decoded = jwt.decode(token);
            if (!decoded) {
              return res.status(401).json({
                success: false,
                message: 'Could not decode token.',
                code: 'INVALID_TOKEN'
              });
            }
            console.log('⚠️  Using decoded token without verification (development only)');
          } catch (decodeError) {
            return res.status(401).json({
              success: false,
              message: 'Token decode failed.',
              code: 'TOKEN_DECODE_FAILED',
              debug: decodeError.message
            });
          }
        } else {
          // In production, return verification error
          return res.status(401).json({
            success: false,
            message: 'Token verification failed.',
            code: 'TOKEN_VERIFICATION_FAILED',
            debug: isDevelopment ? verifyError.message : undefined
          });
        }
      }
    } else {
      // Only allow decode without verification in development
      if (isDevelopment) {
        console.warn('⚠️  Using JWT decode without verification (development mode)');
        console.warn('   Please configure SUPABASE_JWT_SECRET for production');
        decoded = jwt.decode(token);
      } else {
        return res.status(500).json({
          success: false,
          message: 'Server configuration error: JWT secret not configured.',
          code: 'JWT_SECRET_MISSING'
        });
      }
    }

    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token - could not decode.',
        code: 'INVALID_TOKEN'
      });
    }

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTime) {
      return res.status(401).json({
        success: false,
        message: 'Token has expired.',
        code: 'TOKEN_EXPIRED'
      });
    }

    // Add user information to request object
    req.user = {
      id: decoded.sub, // Supabase user ID
      email: decoded.email,
      role: decoded.role || 'authenticated',
      aud: decoded.aud,
      exp: decoded.exp,
      iat: decoded.iat,
      iss: decoded.iss,
      metadata: decoded.user_metadata || {},
      appMetadata: decoded.app_metadata || {}
    };

    // Log successful authentication (optional, remove in production)
    console.log(`✅ Authenticated user: ${req.user.email} (${req.user.id})`);

    next();
  } catch (error) {
    console.error('JWT Verification Error:', error.message);

    // Handle specific JWT errors
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.',
        code: 'INVALID_TOKEN'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired.',
        code: 'TOKEN_EXPIRED'
      });
    }

    if (error.name === 'NotBeforeError') {
      return res.status(401).json({
        success: false,
        message: 'Token not active yet.',
        code: 'TOKEN_NOT_ACTIVE'
      });
    }

    // Generic error
    return res.status(401).json({
      success: false,
      message: 'Token verification failed.',
      code: 'TOKEN_VERIFICATION_FAILED',
      ...(process.env.NODE_ENV === 'development' && { error: error.message })
    });
  }
};

/**
 * Optional middleware to verify user roles
 * Usage: verifyRole(['admin', 'manager'])
 */
const verifyRole = (allowedRoles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required.',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRole = req.user.role || 'authenticated';
    
    if (allowedRoles.length > 0 && !allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions.',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: allowedRoles,
        current: userRole
      });
    }

    next();
  };
};

/**
 * Middleware to extract user info without requiring authentication
 * Useful for optional authentication scenarios
 */
const optionalAuth = (req, _res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      req.user = null;
      return next();
    }

    const token = authHeader.startsWith('Bearer ')
      ? authHeader.slice(7)
      : authHeader;

    if (!token) {
      req.user = null;
      return next();
    }

    let decoded;

    // Try to verify with JWT secret if available, otherwise decode
    if (SUPABASE_JWT_SECRET && SUPABASE_JWT_SECRET !== 'your-supabase-jwt-secret-here' && SUPABASE_JWT_SECRET !== 'super-secret-jwt-token-with-at-least-32-characters-long') {
      try {
        decoded = jwt.verify(token, SUPABASE_JWT_SECRET, {
          algorithms: ['HS256'],
        });
      } catch (verifyError) {
        decoded = jwt.decode(token);
      }
    } else {
      decoded = jwt.decode(token);
    }

    if (decoded) {
      req.user = {
        id: decoded.sub,
        email: decoded.email,
        role: decoded.role || 'authenticated',
        metadata: decoded.user_metadata || {},
        appMetadata: decoded.app_metadata || {}
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    // If token is invalid, continue without user
    req.user = null;
    next();
  }
};

module.exports = {
  verifySupabaseToken,
  verifyRole,
  optionalAuth
};
