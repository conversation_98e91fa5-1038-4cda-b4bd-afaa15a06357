/**
 * Data Isolation Middleware
 * 
 * This middleware provides an additional layer of security to ensure
 * that users can only access their own data. It works in conjunction
 * with the existing Supabase authentication middleware.
 */

const mongoose = require('mongoose');

/**
 * Middleware to ensure data isolation for resource access
 * This middleware checks that the requested resource belongs to the authenticated user
 */
const ensureResourceOwnership = (Model, resourceIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id;
      const resourceId = req.params[resourceIdParam];

      // Skip if no user ID (should be caught by auth middleware)
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // Skip if no resource ID (for list endpoints)
      if (!resourceId) {
        return next();
      }

      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(resourceId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid resource ID format'
        });
      }

      // Check if resource exists and belongs to user
      const resource = await Model.findOne({
        _id: resourceId,
        userId: userId
      });

      if (!resource) {
        return res.status(404).json({
          success: false,
          message: 'Resource not found or access denied'
        });
      }

      // Attach resource to request for use in controller
      req.resource = resource;
      next();
    } catch (error) {
      console.error('Resource ownership check error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during authorization'
      });
    }
  };
};

/**
 * Middleware to automatically add userId to request body for create operations
 */
const addUserIdToBody = (req, res, next) => {
  const userId = req.user?.id;
  
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Add userId to request body
  req.body.userId = userId;
  next();
};

/**
 * Middleware to filter query results by userId for list operations
 */
const filterByUserId = (req, res, next) => {
  const userId = req.user?.id;
  
  if (!userId) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Add userId filter to query
  req.userFilter = { userId };
  next();
};

/**
 * Middleware to validate that bulk operations only affect user's own data
 */
const validateBulkOperations = (Model) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id;
      const { ids } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      if (!ids || !Array.isArray(ids)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or missing IDs array'
        });
      }

      // Validate all IDs are valid ObjectIds
      const invalidIds = ids.filter(id => !mongoose.Types.ObjectId.isValid(id));
      if (invalidIds.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid ID format in bulk operation'
        });
      }

      // Check that all resources belong to the user
      const resources = await Model.find({
        _id: { $in: ids },
        userId: userId
      });

      if (resources.length !== ids.length) {
        return res.status(403).json({
          success: false,
          message: 'Some resources not found or access denied'
        });
      }

      // Attach resources to request
      req.bulkResources = resources;
      next();
    } catch (error) {
      console.error('Bulk operation validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during bulk validation'
      });
    }
  };
};

/**
 * Middleware to log data access for security auditing
 */
const auditDataAccess = (operation, resourceType) => {
  return (req, res, next) => {
    const userId = req.user?.id;
    const resourceId = req.params.id;
    const userAgent = req.get('User-Agent');
    const ip = req.ip || req.connection.remoteAddress;

    // Log the access attempt
    console.log(`[AUDIT] ${new Date().toISOString()} - User: ${userId} - Operation: ${operation} - Resource: ${resourceType}${resourceId ? ` (${resourceId})` : ''} - IP: ${ip} - UserAgent: ${userAgent}`);

    next();
  };
};

/**
 * Middleware to prevent access to system/admin resources
 */
const preventSystemResourceAccess = (req, res, next) => {
  const userId = req.user?.id;
  
  // Check if user is trying to access system resources
  if (req.body.userId && req.body.userId !== userId) {
    return res.status(403).json({
      success: false,
      message: 'Cannot access or modify other users\' data'
    });
  }

  // Check for attempts to modify userId in updates
  if (req.method === 'PUT' || req.method === 'PATCH') {
    if (req.body.userId && req.body.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Cannot change resource ownership'
      });
    }
  }

  next();
};

/**
 * Comprehensive data isolation middleware that combines all protections
 */
const dataIsolationSuite = (Model, options = {}) => {
  const {
    resourceIdParam = 'id',
    enableAudit = true,
    resourceType = 'resource'
  } = options;

  return {
    // For GET /resource/:id - ensure ownership
    ensureOwnership: ensureResourceOwnership(Model, resourceIdParam),
    
    // For POST /resource - add userId and prevent system access
    createProtection: [
      addUserIdToBody,
      preventSystemResourceAccess,
      ...(enableAudit ? [auditDataAccess('CREATE', resourceType)] : [])
    ],
    
    // For GET /resource - filter by userId
    listProtection: [
      filterByUserId,
      ...(enableAudit ? [auditDataAccess('LIST', resourceType)] : [])
    ],
    
    // For PUT/PATCH /resource/:id - ensure ownership and prevent system access
    updateProtection: [
      ensureResourceOwnership(Model, resourceIdParam),
      preventSystemResourceAccess,
      ...(enableAudit ? [auditDataAccess('UPDATE', resourceType)] : [])
    ],
    
    // For DELETE /resource/:id - ensure ownership
    deleteProtection: [
      ensureResourceOwnership(Model, resourceIdParam),
      ...(enableAudit ? [auditDataAccess('DELETE', resourceType)] : [])
    ],
    
    // For bulk operations
    bulkProtection: [
      validateBulkOperations(Model),
      preventSystemResourceAccess,
      ...(enableAudit ? [auditDataAccess('BULK', resourceType)] : [])
    ]
  };
};

module.exports = {
  ensureResourceOwnership,
  addUserIdToBody,
  filterByUserId,
  validateBulkOperations,
  auditDataAccess,
  preventSystemResourceAccess,
  dataIsolationSuite
};
