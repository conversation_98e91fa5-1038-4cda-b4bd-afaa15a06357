# Backend Invoice API Documentation

## Overview
A RESTful API for managing invoices and clients with Supabase JWT authentication.

**Base URL:** `http://localhost:5001/api`

## Authentication

All protected routes require a valid Supabase JWT token in the Authorization header:

```
Authorization: Bearer <your-supabase-jwt-token>
```

### Authentication Endpoints

#### Test Authentication
- **GET** `/auth-test/public` - Public route (no auth required)
- **GET** `/auth-test/optional` - Optional auth route
- **GET** `/auth-test/protected` - Protected route (auth required)
- **POST** `/auth-test/echo` - Echo request data (auth required)

## Client Management

All client endpoints require authentication.

### Get All Clients
**GET** `/clients`

**Query Parameters:**
- `page` (number, default: 1) - Page number for pagination
- `limit` (number, default: 10) - Number of clients per page
- `search` (string) - Search by name, email, or company name
- `status` (string) - Filter by status: Active, Inactive, Suspended
- `sort` (string, default: -createdAt) - Sort field and order

**Response:**
```json
{
  "success": true,
  "count": 10,
  "totalClients": 25,
  "totalPages": 3,
  "currentPage": 1,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalPages": 3,
    "totalClients": 25,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### Get Single Client
**GET** `/clients/:id`

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "...",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+*********0",
    "company": {
      "name": "Acme Corp",
      "website": "https://acme.com",
      "taxId": "*********"
    },
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "United States"
    },
    "currency": "USD",
    "paymentTerms": "Net 30",
    "status": "Active",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### Create Client
**POST** `/clients`

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+*********0",
  "company": {
    "name": "Acme Corp",
    "website": "https://acme.com",
    "taxId": "*********"
  },
  "address": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "country": "United States"
  },
  "currency": "USD",
  "paymentTerms": "Net 30",
  "notes": "Important client"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Client created successfully",
  "data": { ... }
}
```

### Update Client
**PUT** `/clients/:id`

**Request Body:** Same as create client (partial updates allowed)

**Response:**
```json
{
  "success": true,
  "message": "Client updated successfully",
  "data": { ... }
}
```

### Delete Client
**DELETE** `/clients/:id`

**Response:**
```json
{
  "success": true,
  "message": "Client deleted successfully",
  "data": { ... }
}
```

### Get Client Statistics
**GET** `/clients/stats`

**Response:**
```json
{
  "success": true,
  "data": {
    "totalClients": 25,
    "activeClients": 20,
    "inactiveClients": 5,
    "totalInvoiceAmount": 50000,
    "totalOutstanding": 15000
  }
}
```

## Error Responses

### Authentication Errors
```json
{
  "success": false,
  "message": "Access denied. No token provided.",
  "code": "NO_TOKEN"
}
```

```json
{
  "success": false,
  "message": "Invalid token.",
  "code": "INVALID_TOKEN"
}
```

```json
{
  "success": false,
  "message": "Token has expired.",
  "code": "TOKEN_EXPIRED"
}
```

### Validation Errors
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "Client name is required",
    "Please enter a valid email address"
  ]
}
```

### Not Found Errors
```json
{
  "success": false,
  "message": "Client not found"
}
```

## Client Model Schema

### Required Fields
- `name` (string, max 100 chars) - Client name
- `email` (string, valid email) - Client email
- `userId` (string) - Supabase user ID (auto-added)

### Optional Fields
- `phone` (string) - Phone number
- `company.name` (string, max 100 chars) - Company name
- `company.website` (string, valid URL) - Company website
- `company.taxId` (string, max 50 chars) - Tax ID
- `address.street` (string, max 200 chars) - Street address
- `address.city` (string, max 50 chars) - City
- `address.state` (string, max 50 chars) - State
- `address.zipCode` (string, max 20 chars) - Zip code
- `address.country` (string, max 50 chars, default: "United States") - Country
- `currency` (enum: USD, EUR, GBP, CAD, AUD, INR, JPY, default: USD) - Currency
- `paymentTerms` (enum: Net 15, Net 30, Net 45, Net 60, Due on Receipt, Custom, default: Net 30) - Payment terms
- `creditLimit` (number, min 0, default: 0) - Credit limit
- `status` (enum: Active, Inactive, Suspended, default: Active) - Client status
- `notes` (string, max 1000 chars) - Notes
- `tags` (array of strings, max 30 chars each) - Tags

### Auto-Generated Fields
- `_id` - MongoDB ObjectId
- `userId` - Supabase user ID
- `totalInvoices` - Total number of invoices
- `totalAmount` - Total invoice amount
- `outstandingAmount` - Outstanding amount
- `lastInvoiceDate` - Last invoice date
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

## Environment Variables

```env
NODE_ENV=development
PORT=5001
MONGODB_URI=your_mongodb_connection_string
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
```

## Getting Started

1. Install dependencies: `npm install`
2. Set up environment variables in `.env`
3. Start development server: `npm run dev`
4. API will be available at `http://localhost:5001`

## Testing with Frontend

To test with your Nuxt.js frontend, make sure to:

1. Include the Supabase JWT token in API requests
2. Set the backend URL in your frontend configuration
3. Handle authentication errors appropriately

Example frontend API call:
```javascript
const { data } = await $fetch('/api/clients', {
  baseURL: 'http://localhost:5001',
  headers: {
    Authorization: `Bearer ${supabaseToken}`
  }
})
```
