const mongoose = require('mongoose');
const Client = require('../models/Client');
const Invoice = require('../models/Invoice');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedReportsData = async () => {
  try {
    const testUserId = '9799afbc-00e8-4678-8526-e171e357a093'; // Your actual user ID

    // Clear existing test data
    await Client.deleteMany({ userId: testUserId });
    await Invoice.deleteMany({ userId: testUserId });

    console.log('🧹 Cleared existing test data');

    // Create sample clients
    const clients = [
      {
        userId: testUserId,
        name: 'Tech Solutions Ltd',
        email: '<EMAIL>',
        type: 'Business',
        phone: '******-0101',
        company: { name: 'Tech Solutions Ltd' },
        status: 'Active',
        createdAt: new Date('2024-01-15')
      },
      {
        userId: testUserId,
        name: 'John Wilson',
        email: '<EMAIL>',
        type: 'Individual',
        phone: '******-0102',
        status: 'Active',
        createdAt: new Date('2024-02-10')
      },
      {
        userId: testUserId,
        name: 'Acme Corporation',
        email: '<EMAIL>',
        type: 'Business',
        phone: '******-0103',
        company: { name: 'Acme Corporation' },
        status: 'Active',
        createdAt: new Date('2024-03-05')
      },
      {
        userId: testUserId,
        name: 'Marketing Inc',
        email: '<EMAIL>',
        type: 'Business',
        phone: '******-0104',
        company: { name: 'Marketing Inc' },
        status: 'Inactive',
        createdAt: new Date('2024-04-20')
      },
      {
        userId: testUserId,
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        type: 'Individual',
        phone: '******-0105',
        status: 'Active',
        createdAt: new Date('2024-11-01')
      },
      {
        userId: testUserId,
        name: 'Global Enterprises',
        email: '<EMAIL>',
        type: 'Business',
        phone: '******-0106',
        company: { name: 'Global Enterprises' },
        status: 'Active',
        createdAt: new Date('2024-12-01')
      }
    ];

    // Create clients one by one to handle clientId generation
    const createdClients = [];
    for (const clientData of clients) {
      try {
        const client = new Client(clientData);
        const savedClient = await client.save();
        createdClients.push(savedClient);
      } catch (error) {
        console.warn(`⚠️ Skipping client ${clientData.name}:`, error.message);
      }
    }
    console.log(`✅ Created ${createdClients.length} clients`);

    // Create sample invoices
    const invoices = [];
    const currentDate = new Date();
    
    // Tech Solutions Ltd - 12 invoices
    for (let i = 0; i < 12; i++) {
      const invoiceDate = new Date(currentDate);
      invoiceDate.setMonth(currentDate.getMonth() - Math.floor(i / 3));
      invoiceDate.setDate(Math.floor(Math.random() * 28) + 1);
      
      invoices.push({
        userId: testUserId,
        invoiceNumber: `INV-2024${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(i + 1).padStart(3, '0')}`,
        clientName: 'Tech Solutions Ltd',
        clientEmail: '<EMAIL>',
        items: [{
          description: 'Software Development Services',
          quantity: 1,
          unitPrice: 3500 + (Math.random() * 1000),
          total: 3500 + (Math.random() * 1000)
        }],
        subtotal: 3500 + (Math.random() * 1000),
        total: 3500 + (Math.random() * 1000),
        status: 'paid',
        issueDate: invoiceDate,
        dueDate: new Date(invoiceDate.getTime() + 30 * 24 * 60 * 60 * 1000),
        createdAt: invoiceDate
      });
    }

    // John Wilson - 15 invoices
    for (let i = 0; i < 15; i++) {
      const invoiceDate = new Date(currentDate);
      invoiceDate.setMonth(currentDate.getMonth() - Math.floor(i / 4));
      invoiceDate.setDate(Math.floor(Math.random() * 28) + 1);
      
      invoices.push({
        userId: testUserId,
        invoiceNumber: `INV-2024${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(i + 100).padStart(3, '0')}`,
        clientName: 'John Wilson',
        clientEmail: '<EMAIL>',
        items: [{
          description: 'Consulting Services',
          quantity: 1,
          unitPrice: 2000 + (Math.random() * 500),
          total: 2000 + (Math.random() * 500)
        }],
        subtotal: 2000 + (Math.random() * 500),
        total: 2000 + (Math.random() * 500),
        status: 'paid',
        issueDate: invoiceDate,
        dueDate: new Date(invoiceDate.getTime() + 30 * 24 * 60 * 60 * 1000),
        createdAt: invoiceDate
      });
    }

    // Acme Corporation - 8 invoices
    for (let i = 0; i < 8; i++) {
      const invoiceDate = new Date(currentDate);
      invoiceDate.setMonth(currentDate.getMonth() - Math.floor(i / 2));
      invoiceDate.setDate(Math.floor(Math.random() * 28) + 1);
      
      invoices.push({
        userId: testUserId,
        invoiceNumber: `INV-2024${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(i + 200).padStart(3, '0')}`,
        clientName: 'Acme Corporation',
        clientEmail: '<EMAIL>',
        items: [{
          description: 'Web Development',
          quantity: 1,
          unitPrice: 3000 + (Math.random() * 1000),
          total: 3000 + (Math.random() * 1000)
        }],
        subtotal: 3000 + (Math.random() * 1000),
        total: 3000 + (Math.random() * 1000),
        status: 'paid',
        issueDate: invoiceDate,
        dueDate: new Date(invoiceDate.getTime() + 30 * 24 * 60 * 60 * 1000),
        createdAt: invoiceDate
      });
    }

    // Add more invoices for other clients...
    const createdInvoices = await Invoice.insertMany(invoices);
    console.log(`✅ Created ${createdInvoices.length} invoices`);

    console.log('🎉 Sample data seeded successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding data:', error);
  }
};

const runSeed = async () => {
  await connectDB();
  await seedReportsData();
  await mongoose.disconnect();
  console.log('✅ Database connection closed');
};

runSeed();
