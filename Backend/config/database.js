const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    // Check if MONGODB_URI is provided
    if (!process.env.MONGODB_URI) {
      console.warn('⚠️  MONGODB_URI not provided. Database features will be disabled.');
      console.warn('   Please add your MongoDB connection string to the .env file');
      return;
    }

    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      // Temporary SSL bypass due to system date issue
      // TODO: Fix system date and remove these options
      tls: true,
      tlsAllowInvalidCertificates: true,
      tlsAllowInvalidHostnames: true,
    });

    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);

    // Connection event listeners
    mongoose.connection.on('connected', () => {
      console.log('🔗 Mongoose connected to MongoDB');
    });

    mongoose.connection.on('error', (err) => {
      console.error('❌ Mongoose connection error:', err.message);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('🔌 Mongoose disconnected from MongoDB');
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log('MongoDB connection closed through app termination');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Error connecting to MongoDB:', error.message);
    console.warn('⚠️  Server will continue without database connection');
    console.warn('   Database-dependent features may not work properly');
    // Don't exit the process, let the server continue without DB
  }
};

module.exports = connectDB;
