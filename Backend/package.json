{"name": "backend-invoice", "version": "1.0.0", "description": "Backend API for Invoice Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Nawinkishore/Backend-invoice.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Nawinkishore/Backend-invoice/issues"}, "homepage": "https://github.com/Nawinkishore/Backend-invoice#readme", "dependencies": {"@supabase/supabase-js": "^2.50.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "mongoose": "^8.15.1", "vue-toastification": "^1.7.14"}, "devDependencies": {"nodemon": "^3.1.10"}}