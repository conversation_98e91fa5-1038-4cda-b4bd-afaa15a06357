export default {
  content: [
    './components/**/*.{vue,js,ts,jsx,tsx}',
    './pages/**/*.{vue,js,ts,jsx,tsx}',
    './layouts/**/*.{vue,js,ts,jsx,tsx}',
    './plugins/**/*.{js,ts}',
    './nuxt.config.{js,ts}',
    './assets/css/**/*.css',
  ],
  theme: {
    extend: {
      scrollBehavior: ['smooth'],
      colors: {
        // your custom colors if needed
      },
      fontFamily: {
        cursive: ['"Comic Sans MS"', 'cursive'],
      },
    },
  },
  corePlugins: {
    preflight: true,
  },
  safelist: [
    'border',
    'border-gray-300',
    'bg-white',
    'text-gray-700',
    'hover:bg-gray-100',
    'text-red-600',
    'bg-blue-600',
    'hover:bg-blue-700',
    'text-white',
    'rounded-md',
    'px-3',
    'py-2',
    'py-1',
    'w-full',
    'text-sm',
    'text-lg',
    'font-bold',
    'text-xl',
    'shadow',
    'gap-1',
    'gap-2',
    'grid',
    'flex',
    'items-center',
    'justify-end',
    'justify-between',
    'mx-auto',
    'max-w-5xl',
    'space-y-2',
    'space-y-6',
    'mb-2',
    'mb-4',
    'mt-6',
  ],
  plugins: [],
}
