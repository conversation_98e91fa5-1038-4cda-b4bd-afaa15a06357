// middleware/auth-guest.ts
import { useAuthStore } from '~/stores/auth'

export default defineNuxtRouteMiddleware(async (to, from) => {
  const authStore = useAuthStore()

  // Initialize auth store if not already done
  if (!authStore.initialized) {
    await authStore.fetchUser()
  }

  // If user is authenticated, redirect to dashboard
  if (authStore.isAuthenticated) {
    console.log('User already authenticated, redirecting to dashboard')
    return navigateTo('/')
  }
})
