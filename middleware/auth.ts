// middleware/auth.ts
import { useAuthStore } from '~/stores/auth'

export default defineNuxtRouteMiddleware(async (to, from) => {
  // Skip auth check for client-side navigation to auth pages
  if (to.path.startsWith('/Auth/')) {
    return
  }

  const authStore = useAuthStore()

  try {
    // Initialize auth store if not already done
    if (!authStore.initialized) {
      console.log('🔄 Initializing auth store...')
      await authStore.fetchUser()
    }

    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      console.log('❌ User not authenticated, redirecting to login')

      // Store the intended destination for redirect after login
      const redirectTo = to.fullPath !== '/' ? to.fullPath : undefined
      const loginPath = redirectTo ? `/Auth/login?redirect=${encodeURIComponent(redirectTo)}` : '/Auth/login'

      return navigateTo(loginPath)
    }

    // Check token expiration and refresh if needed
    if (authStore.session?.expires_at) {
      const now = Math.floor(Date.now() / 1000)
      const tokenExp = authStore.session.expires_at
      const timeUntilExpiry = tokenExp - now

      // If token expires in less than 5 minutes, try to refresh
      if (timeUntilExpiry > 0 && timeUntilExpiry < 300) {
        console.log('🔄 Token expiring soon, attempting refresh...')
        const refreshed = await authStore.refreshSession()

        if (!refreshed) {
          console.log('❌ Token refresh failed, redirecting to login')
          return navigateTo('/Auth/login')
        }
      } else if (timeUntilExpiry <= 0) {
        console.log('❌ Token expired, redirecting to login')
        return navigateTo('/Auth/login')
      }
    }

    console.log('✅ User authenticated:', authStore.userEmail)
  } catch (error: any) {
    console.error('❌ Auth middleware error:', error)

    // On any auth error, redirect to login
    return navigateTo('/Auth/login')
  }
})
