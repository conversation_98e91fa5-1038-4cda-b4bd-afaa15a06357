<template>
  <div class="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
      <h1 class="text-2xl font-bold text-center mb-6">Authentication Test</h1>
      
      <!-- Auth Status -->
      <div class="mb-6 p-4 rounded-lg" :class="authStore.isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
        <h2 class="font-semibold mb-2">Authentication Status</h2>
        <p><strong>Authenticated:</strong> {{ authStore.isAuthenticated ? 'Yes' : 'No' }}</p>
        <p><strong>Loading:</strong> {{ authStore.loading ? 'Yes' : 'No' }}</p>
        <p><strong>Initialized:</strong> {{ authStore.initialized ? 'Yes' : 'No' }}</p>
      </div>

      <!-- User Info -->
      <div v-if="authStore.isAuthenticated" class="mb-6 p-4 bg-blue-100 rounded-lg">
        <h2 class="font-semibold mb-2 text-blue-800">User Information</h2>
        <p><strong>Email:</strong> {{ authStore.userEmail }}</p>
        <p><strong>Name:</strong> {{ authStore.userName }}</p>
        <p><strong>User ID:</strong> {{ authStore.user?.id }}</p>
      </div>

      <!-- Session Info -->
      <div v-if="authStore.session" class="mb-6 p-4 bg-purple-100 rounded-lg">
        <h2 class="font-semibold mb-2 text-purple-800">Session Information</h2>
        <p><strong>Access Token:</strong> {{ authStore.session.access_token ? 'Present' : 'Missing' }}</p>
        <p><strong>Refresh Token:</strong> {{ authStore.session.refresh_token ? 'Present' : 'Missing' }}</p>
        <p><strong>Expires At:</strong> {{ new Date(authStore.session.expires_at * 1000).toLocaleString() }}</p>
        <p><strong>Time Until Expiry:</strong> {{ timeUntilExpiry }} minutes</p>
      </div>

      <!-- Actions -->
      <div class="space-y-3">
        <button 
          @click="refreshUser" 
          :disabled="authStore.loading"
          class="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ authStore.loading ? 'Loading...' : 'Refresh User Data' }}
        </button>

        <button
          v-if="authStore.isAuthenticated"
          @click="refreshSession"
          :disabled="authStore.loading"
          class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 disabled:opacity-50"
        >
          Refresh Session
        </button>

        <button
          v-if="authStore.isAuthenticated"
          @click="testBackendAuth"
          :disabled="testing"
          class="w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {{ testing ? 'Testing...' : 'Test Backend Auth' }}
        </button>

        <button
          v-if="authStore.isAuthenticated"
          @click="handleLogout"
          :disabled="authStore.loading"
          class="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 disabled:opacity-50"
        >
          Logout
        </button>

        <NuxtLink 
          v-if="!authStore.isAuthenticated"
          to="/Auth/login"
          class="block w-full bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600 text-center"
        >
          Go to Login
        </NuxtLink>
      </div>

      <!-- Messages -->
      <div v-if="message" class="mt-4 p-3 rounded" :class="messageType === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '~/stores/auth'

const authStore = useAuthStore()
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const testing = ref(false)

const timeUntilExpiry = computed(() => {
  if (!authStore.session?.expires_at) return 'Unknown'
  const now = Math.floor(Date.now() / 1000)
  const expiresAt = authStore.session.expires_at
  const minutes = Math.floor((expiresAt - now) / 60)
  return minutes > 0 ? minutes : 'Expired'
})

const showMessage = (msg: string, type: 'success' | 'error' = 'success') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

const refreshUser = async () => {
  try {
    const success = await authStore.fetchUser()
    showMessage(success ? 'User data refreshed successfully' : 'Failed to refresh user data', success ? 'success' : 'error')
  } catch (error: any) {
    showMessage(`Error: ${error.message}`, 'error')
  }
}

const refreshSession = async () => {
  try {
    const success = await authStore.refreshSession()
    showMessage(success ? 'Session refreshed successfully' : 'Failed to refresh session', success ? 'success' : 'error')
  } catch (error: any) {
    showMessage(`Error: ${error.message}`, 'error')
  }
}

const testBackendAuth = async () => {
  testing.value = true
  try {
    const config = useRuntimeConfig()
    const response = await fetch(`${config.public.apiBaseUrl}/auth-test/protected`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.session?.access_token}`,
        'Content-Type': 'application/json'
      }
    })

    const data = await response.json()

    if (response.ok) {
      showMessage(`Backend auth test successful: ${data.message}`)
    } else {
      showMessage(`Backend auth test failed: ${data.message}`, 'error')
    }
  } catch (error: any) {
    showMessage(`Backend auth test error: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    showMessage('Logged out successfully')
  } catch (error: any) {
    showMessage(`Logout error: ${error.message}`, 'error')
  }
}

// Initialize auth state on component mount
onMounted(async () => {
  if (!authStore.initialized) {
    await authStore.fetchUser()
  }
})
</script>
