<template>
  <div>
          <!-- Heading -->
      <Heading
    title="Payments"
    description="Track and manage all payment transactions."
    icon="lucide:indian-rupee"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'Send payment request',
        icon: 'lucide:send',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments/PaymentRequests'
      },
      {
        label: 'Add Payment',
        icon: 'lucide:plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments/PaymentMethod'
      },
    ]"
  />
  <div>
    <PaymentStats/>
  </div>
  <div>
    <PaymentsTable/>
  </div>
  </div>
</template>

<script lang="ts" setup>

</script>

<style>

</style>