<template>
        <!-- Heading -->
      <Heading
    title="Add payments method"
    description="Track and manage all payment transactions."
    icon="lucide:indian-rupee"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'Back',
        icon: 'lucide:arrow-left',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments'
      },
      {
        label: 'Add Payment',
        icon: 'lucide:plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/payments/choosemethod'
      },
    ]"
  />
  <div class="p-6 bg-gray-50 min-h-screen">
    <PaymentMethodDashboardStats />

    <div class="bg-white rounded-xl shadow-md p-6 mt-4">
      <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
        <CreditCard class="w-5 h-5 mr-2" /> Your Payment Methods
      </h2>
      <div class="mb-4">
        <input
          v-model="search"
          type="text"
          placeholder="Search payment methods..."
          class="w-full px-4 py-2 border rounded-lg text-sm"
        />
      </div>
      <div class="grid md:grid-cols-2 gap-4">
        <PaymentMethodCard
          :icon="Phone"
          title="Primary UPI"
          subtitle="UPI Payment"
          date="1/15/2024"
          :isDefault="true"
        />
        <PaymentMethodCard
          :icon="CreditCard"
          title="My Credit Card"
          subtitle="JOHN DOE • Exp 12/26"
          date="1/10/2024"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { CreditCard, Phone } from 'lucide-vue-next'
import { ref } from 'vue'

const search = ref('')
</script>
