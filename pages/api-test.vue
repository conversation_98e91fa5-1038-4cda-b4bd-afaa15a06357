<template>
  <div class="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8">Backend API Test</h1>
      
      <!-- Authentication Status -->
      <div class="mb-8 p-6 bg-white rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Authentication Status</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p><strong>Authenticated:</strong> {{ authStore.isAuthenticated ? 'Yes' : 'No' }}</p>
            <p><strong>User Email:</strong> {{ authStore.userEmail || 'N/A' }}</p>
            <p><strong>Token Available:</strong> {{ !!authStore.session?.access_token ? 'Yes' : 'No' }}</p>
          </div>
          <div>
            <button 
              @click="testAuth" 
              :disabled="!authStore.isAuthenticated || loading.auth"
              class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {{ loading.auth ? 'Testing...' : 'Test Backend Auth' }}
            </button>
          </div>
        </div>
        
        <div v-if="authTestResult" class="mt-4 p-3 rounded" :class="authTestResult.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
          <pre class="text-sm">{{ JSON.stringify(authTestResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- Client Management Test -->
      <div class="mb-8 p-6 bg-white rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Client Management Test</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <button 
            @click="getClients" 
            :disabled="!authStore.isAuthenticated || loading.clients"
            class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
          >
            {{ loading.clients ? 'Loading...' : 'Get Clients' }}
          </button>
          
          <button 
            @click="getClientStats" 
            :disabled="!authStore.isAuthenticated || loading.stats"
            class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {{ loading.stats ? 'Loading...' : 'Get Stats' }}
          </button>
          
          <button 
            @click="createTestClient" 
            :disabled="!authStore.isAuthenticated || loading.create"
            class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
          >
            {{ loading.create ? 'Creating...' : 'Create Test Client' }}
          </button>
        </div>

        <!-- Client Stats -->
        <div v-if="clientStats" class="mb-4 p-4 bg-gray-50 rounded">
          <h3 class="font-semibold mb-2">Client Statistics</h3>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
            <div>
              <p class="font-medium">Total Clients</p>
              <p class="text-2xl font-bold text-blue-600">{{ clientStats.totalClients }}</p>
            </div>
            <div>
              <p class="font-medium">Active</p>
              <p class="text-2xl font-bold text-green-600">{{ clientStats.activeClients }}</p>
            </div>
            <div>
              <p class="font-medium">Inactive</p>
              <p class="text-2xl font-bold text-red-600">{{ clientStats.inactiveClients }}</p>
            </div>
            <div>
              <p class="font-medium">Total Amount</p>
              <p class="text-2xl font-bold text-purple-600">${{ clientStats.totalInvoiceAmount }}</p>
            </div>
            <div>
              <p class="font-medium">Outstanding</p>
              <p class="text-2xl font-bold text-orange-600">${{ clientStats.totalOutstanding }}</p>
            </div>
          </div>
        </div>

        <!-- Clients List -->
        <div v-if="clients.length > 0" class="mb-4">
          <h3 class="font-semibold mb-2">Clients ({{ clients.length }})</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Company</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr v-for="client in clients" :key="client._id">
                  <td class="px-4 py-2 text-sm">{{ client.name }}</td>
                  <td class="px-4 py-2 text-sm">{{ client.email }}</td>
                  <td class="px-4 py-2 text-sm">{{ client.company?.name || 'N/A' }}</td>
                  <td class="px-4 py-2 text-sm">
                    <span class="px-2 py-1 text-xs rounded-full" :class="getStatusClass(client.status)">
                      {{ client.status }}
                    </span>
                  </td>
                  <td class="px-4 py-2 text-sm">
                    <button 
                      @click="deleteClient(client._id!)" 
                      :disabled="loading.delete"
                      class="text-red-600 hover:text-red-800 disabled:opacity-50"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="mt-4 p-3 bg-red-100 text-red-700 rounded">
          <strong>Error:</strong> {{ error }}
        </div>
      </div>

      <!-- Raw Response Display -->
      <div v-if="lastResponse" class="p-6 bg-white rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Last API Response</h2>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto">{{ JSON.stringify(lastResponse, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { clientApi, type Client } from '~/services/clientApi'

definePageMeta({
  middleware: 'auth'
})

const authStore = useAuthStore()

// Reactive state
const clients = ref<Client[]>([])
const clientStats = ref<any>(null)
const authTestResult = ref<any>(null)
const lastResponse = ref<any>(null)
const error = ref<string>('')

const loading = ref({
  auth: false,
  clients: false,
  stats: false,
  create: false,
  delete: false
})

// Helper function for status styling
const getStatusClass = (status?: string) => {
  switch (status) {
    case 'Active':
      return 'bg-green-100 text-green-800'
    case 'Inactive':
      return 'bg-red-100 text-red-800'
    case 'Suspended':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// API test functions
const testAuth = async () => {
  loading.value.auth = true
  error.value = ''
  
  try {
    const result = await clientApi.testAuth()
    authTestResult.value = result
    lastResponse.value = result
  } catch (err: any) {
    error.value = err.message
    authTestResult.value = { success: false, error: err.message }
  } finally {
    loading.value.auth = false
  }
}

const getClients = async () => {
  loading.value.clients = true
  error.value = ''
  
  try {
    const result = await clientApi.getClients({ limit: 20 })
    clients.value = result.data
    lastResponse.value = result
  } catch (err: any) {
    error.value = err.message
  } finally {
    loading.value.clients = false
  }
}

const getClientStats = async () => {
  loading.value.stats = true
  error.value = ''
  
  try {
    const result = await clientApi.getClientStats()
    clientStats.value = result.data
    lastResponse.value = result
  } catch (err: any) {
    error.value = err.message
  } finally {
    loading.value.stats = false
  }
}

const createTestClient = async () => {
  loading.value.create = true
  error.value = ''
  
  try {
    const testClient = {
      name: `Test Client ${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      phone: '+1234567890',
      company: {
        name: 'Test Company',
        website: 'https://test.com'
      },
      address: {
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'United States'
      },
      status: 'Active' as const,
      notes: 'This is a test client created from the frontend'
    }
    
    const result = await clientApi.createClient(testClient)
    lastResponse.value = result
    
    // Refresh clients list
    await getClients()
  } catch (err: any) {
    error.value = err.message
  } finally {
    loading.value.create = false
  }
}

const deleteClient = async (id: string) => {
  loading.value.delete = true
  error.value = ''
  
  try {
    const result = await clientApi.deleteClient(id)
    lastResponse.value = result
    
    // Remove from local list
    clients.value = clients.value.filter(c => c._id !== id)
    
    // Refresh stats
    await getClientStats()
  } catch (err: any) {
    error.value = err.message
  } finally {
    loading.value.delete = false
  }
}

// Initialize data on mount
onMounted(async () => {
  if (authStore.isAuthenticated) {
    await Promise.all([
      getClientStats(),
      getClients()
    ])
  }
})
</script>
