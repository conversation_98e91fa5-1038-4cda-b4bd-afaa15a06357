<template>
  <div>
    <!-- Heading -->
    <Heading
      title="Profile Settings"
      description="Manage your personal information and account preferences"
      icon="lucide:user"
      iconColor="text-green-400"
      bgColor="bg-white"
    />

    <div class="px-4 lg:px-8 py-6">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        <span class="ml-3 text-gray-600">Loading profile...</span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex">
          <Icon name="lucide:alert-circle" class="h-5 w-5 text-red-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error loading profile</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
            <button
              @click="retryLoad"
              class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          </div>
        </div>
      </div>

      <!-- Profile Content -->
      <div v-else class="max-w-4xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Profile Summary Card -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border p-6">
              <div class="text-center">
                <div class="w-24 h-24 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <Icon name="lucide:user" class="w-12 h-12 text-green-600" />
                </div>
                <h3 class="text-lg font-medium text-gray-900">{{ userDisplayName }}</h3>
                <p class="text-sm text-gray-500">{{ profile?.email }}</p>
                <p class="text-xs text-gray-400 mt-2">
                  Member since {{ formatDate(profile?.created_at) }}
                </p>
              </div>
            </div>
          </div>

          <!-- Profile Form -->
          <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Personal Information</h3>
                <p class="text-sm text-gray-500">Update your personal details and contact information.</p>
              </div>

              <form @submit.prevent="handleUpdateProfile" class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <input
                      v-model="profileForm.full_name"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      :value="profile?.email"
                      type="email"
                      disabled
                      class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                      placeholder="Email cannot be changed"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Phone
                    </label>
                    <input
                      v-model="profileForm.phone"
                      type="tel"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Enter your phone number"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Company
                    </label>
                    <input
                      v-model="profileForm.company"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Enter your company name"
                    />
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Address
                  </label>
                  <input
                    v-model="profileForm.address"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter your address"
                  />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      City
                    </label>
                    <input
                      v-model="profileForm.city"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="City"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      State
                    </label>
                    <input
                      v-model="profileForm.state"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="State"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Postal Code
                    </label>
                    <input
                      v-model="profileForm.postal_code"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Postal Code"
                    />
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Country
                    </label>
                    <select
                      v-model="profileForm.country"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="">Select Country</option>
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                      <option value="GB">United Kingdom</option>
                      <option value="IN">India</option>
                      <option value="AU">Australia</option>
                      <option value="DE">Germany</option>
                      <option value="FR">France</option>
                      <option value="JP">Japan</option>
                      <option value="BR">Brazil</option>
                      <option value="MX">Mexico</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Timezone
                    </label>
                    <select
                      v-model="profileForm.timezone"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                      <option value="Europe/London">London</option>
                      <option value="Europe/Paris">Paris</option>
                      <option value="Asia/Tokyo">Tokyo</option>
                      <option value="Asia/Kolkata">India</option>
                      <option value="Australia/Sydney">Sydney</option>
                    </select>
                  </div>
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t">
                  <button
                    type="button"
                    @click="resetForm"
                    class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Reset
                  </button>
                  <button
                    type="submit"
                    :disabled="isUpdating"
                    class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                  >
                    {{ isUpdating ? 'Updating...' : 'Update Profile' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { useUserStore } from '~/stores/user'
import type { UpdateProfileData } from '~/services/userApi'

definePageMeta({
  middleware: 'auth'
})

const toast = useToast()
const userStore = useUserStore()

// Computed properties from store
const profile = computed(() => userStore.profile)
const isLoading = computed(() => userStore.isLoading)
const isUpdating = computed(() => userStore.isUpdating)
const error = computed(() => userStore.error)
const userDisplayName = computed(() => userStore.userDisplayName)

// Form data
const profileForm = reactive<UpdateProfileData>({
  full_name: '',
  phone: '',
  company: '',
  address: '',
  city: '',
  state: '',
  postal_code: '',
  country: '',
  timezone: 'UTC'
})

// Initialize form with profile data
const initializeForm = () => {
  if (profile.value) {
    profileForm.full_name = profile.value.full_name || ''
    profileForm.phone = profile.value.phone || ''
    profileForm.company = profile.value.company || ''
    profileForm.address = profile.value.address || ''
    profileForm.city = profile.value.city || ''
    profileForm.state = profile.value.state || ''
    profileForm.postal_code = profile.value.postal_code || ''
    profileForm.country = profile.value.country || ''
    profileForm.timezone = profile.value.timezone || 'UTC'
  }
}

// Reset form to original values
const resetForm = () => {
  initializeForm()
}

// Handle profile update
const handleUpdateProfile = async () => {
  try {
    await userStore.updateProfile(profileForm)
    toast.success('Profile updated successfully!')
  } catch (error) {
    console.error('Failed to update profile:', error)
    toast.error('Failed to update profile. Please try again.')
  }
}

// Retry loading profile
const retryLoad = async () => {
  try {
    await userStore.fetchProfile()
    initializeForm()
  } catch (error) {
    console.error('Failed to retry loading profile:', error)
  }
}

// Format date helper
const formatDate = (dateString?: string) => {
  if (!dateString) return 'Unknown'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Load profile on mount
onMounted(async () => {
  try {
    if (!profile.value) {
      await userStore.fetchProfile()
    }
    initializeForm()
  } catch (error) {
    console.error('Failed to load profile:', error)
  }
})
</script>
  
  <style>
  /* Add any custom styles if needed */
  </style>
  