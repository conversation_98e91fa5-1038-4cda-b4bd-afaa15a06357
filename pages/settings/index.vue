<script setup lang="ts">
import { <PERSON><PERSON>, Bell, ShieldCheck, File } from 'lucide-vue-next'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs'
</script>

<template>
  <Heading
    title="Manage Settings"
    description="Manage your account and application preferences"
    icon="lucide:settings"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'Update Profile',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/settings',
      },
    ]"
  />

  <div class="py-5 px-4 relative z-0">
    <Tabs default-value="Profile" class="w-full bg-white relative z-10">
      <TabsList
        class="flex flex-wrap gap-5 w-full bg-white z-20"
      >
        <TabsTrigger
          value="Profile"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <User class="w-4 h-4" /> Profile
        </TabsTrigger>
        <TabsTrigger
          value="Notifications"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <Bell class="w-4 h-4" /> Notifications
        </TabsTrigger>
        <TabsTrigger
          value="Security"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <ShieldCheck class="w-4 h-4" /> Security
        </TabsTrigger>
        <TabsTrigger
          value="Templates"
          class="flex items-center gap-2 px-4 py-2 rounded-md"
        >
          <File class="w-4 h-4" /> Templates
        </TabsTrigger>
      </TabsList>

      <TabsContent value="Profile" class="relative z-10 mt-6">
        <Profile class="py-20" />
      </TabsContent>
      <TabsContent value="Notifications" class="relative z-10 mt-6">
        <Notifications class="py-20" />
      </TabsContent>
      <TabsContent value="Security" class="relative z-10 mt-6">
        <Security class="py-20" />
      </TabsContent>
      <TabsContent value="Templates" class="relative z-10 mt-6">
        <InvoiceTemplates />
      </TabsContent>
    </Tabs>
  </div>
</template>
