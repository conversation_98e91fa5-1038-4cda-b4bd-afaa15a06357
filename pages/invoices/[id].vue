<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <button
              @click="router.push('/invoices')"
              class="flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200"
            >
              <Icon name="lucide:arrow-left" class="w-5 h-5 mr-2" />
              Back to Invoices
            </button>
            <div class="h-6 border-l border-gray-300"></div>
            <h1 class="text-2xl font-bold text-gray-900">
              Invoice {{ invoice?.invoiceNumber }}
            </h1>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex items-center space-x-3">
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <button
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  :disabled="isLoading || isProcessing"
                >
                  <Icon v-if="isProcessing" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
                  <Icon v-else name="lucide:more-horizontal" class="w-4 h-4 mr-2" />
                  {{ isProcessing ? 'Processing...' : 'Actions' }}
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-48">
                <DropdownMenuItem @click="handleEdit" class="cursor-pointer">
                  <Icon name="lucide:edit" class="w-4 h-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem @click="handleDownloadPDF" class="cursor-pointer">
                  <Icon name="lucide:download" class="w-4 h-4 mr-2" />
                  Download PDF
                </DropdownMenuItem>
                <DropdownMenuItem @click="handlePrintInvoice" class="cursor-pointer">
                  <Icon name="lucide:printer" class="w-4 h-4 mr-2" />
                  Print Preview
                </DropdownMenuItem>
                <DropdownMenuItem @click="handleSendEmail" class="cursor-pointer">
                  <Icon name="lucide:mail" class="w-4 h-4 mr-2" />
                  Send Email
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  @click="handleMarkAsPaid"
                  class="cursor-pointer"
                  :class="invoice?.isPaid ? 'text-orange-600' : 'text-green-600'"
                  :disabled="isProcessing"
                >
                  <Icon
                    v-if="!isProcessing"
                    :name="invoice?.isPaid ? 'lucide:x-circle' : 'lucide:check-circle'"
                    class="w-4 h-4 mr-2"
                  />
                  <Icon
                    v-else
                    name="lucide:loader-2"
                    class="w-4 h-4 mr-2 animate-spin"
                  />
                  {{ isProcessing ? 'Updating...' : (invoice?.isPaid ? 'Mark as Unpaid' : 'Mark as Paid') }}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem @click="handleDelete" class="cursor-pointer text-red-600">
                  <Icon name="lucide:trash-2" class="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      <span class="ml-3 text-gray-600">Loading invoice...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <Icon name="lucide:alert-circle" class="h-5 w-5 text-red-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error loading invoice</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
            <button 
              @click="loadInvoice"
              class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Invoice Content -->
    <div v-else-if="invoice" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Status Banner -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span
              :class="[
                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                getStatusClass(getDisplayStatus())
              ]"
            >
              <Icon :name="getStatusIcon(getDisplayStatus())" class="w-4 h-4 mr-1" />
              {{ getStatusText(getDisplayStatus()) }}
            </span>
            <span v-if="isOverdue" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
              <Icon name="lucide:clock" class="w-4 h-4 mr-1" />
              Overdue
            </span>
          </div>
          <div class="text-sm text-gray-500">
            Created {{ formatDate(invoice.createdAt) }}
          </div>
        </div>
      </div>

      <!-- Invoice Details Card -->
      <div class="bg-white shadow-sm rounded-lg overflow-hidden">
        <div class="px-6 py-8">
          <!-- Invoice Header -->
          <div class="flex justify-between items-start mb-8">
            <div>
              <h2 class="text-3xl font-bold text-gray-900 mb-2">INVOICE</h2>
              <p class="text-lg text-gray-600">{{ invoice.invoiceNumber }}</p>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-500 mb-1">Total Amount</div>
              <div class="text-3xl font-bold text-gray-900">₹{{ formatCurrency(invoice.total) }}</div>
            </div>
          </div>

          <!-- Client and Invoice Info -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- Bill To -->
            <div>
              <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Bill To</h3>
              <div class="text-gray-900">
                <p class="font-medium text-lg">{{ invoice.clientName || 'No client name' }}</p>
                <p class="text-gray-600">{{ invoice.clientEmail || 'No email provided' }}</p>
                <p v-if="invoice.clientPhone" class="text-gray-600">{{ invoice.clientPhone }}</p>
                <div v-if="invoice.clientAddress && (invoice.clientAddress.street || invoice.clientAddress.city || invoice.clientAddress.state || invoice.clientAddress.zipCode || invoice.clientAddress.country)" class="text-gray-600 mt-2">
                  <p v-if="invoice.clientAddress.street">{{ invoice.clientAddress.street }}</p>
                  <p v-if="invoice.clientAddress.city || invoice.clientAddress.state || invoice.clientAddress.zipCode">
                    <span v-if="invoice.clientAddress.city">{{ invoice.clientAddress.city }}</span>
                    <span v-if="invoice.clientAddress.state">, {{ invoice.clientAddress.state }}</span>
                    <span v-if="invoice.clientAddress.zipCode"> {{ invoice.clientAddress.zipCode }}</span>
                  </p>
                  <p v-if="invoice.clientAddress.country">{{ invoice.clientAddress.country }}</p>
                </div>
                <div v-else class="text-gray-400 text-sm mt-2">
                  No address provided
                </div>
              </div>
            </div>

            <!-- Invoice Details -->
            <div>
              <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Invoice Details</h3>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span class="text-gray-600">Issue Date:</span>
                  <span class="text-gray-900">{{ formatDate(invoice.issueDate) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Due Date:</span>
                  <span class="text-gray-900" :class="{ 'text-red-600 font-medium': isOverdue }">
                    {{ formatDate(invoice.dueDate) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Type:</span>
                  <span class="text-gray-900 capitalize">{{ invoice.invoiceType }}</span>
                </div>
                <div v-if="invoice.paymentTerms" class="flex justify-between">
                  <span class="text-gray-600">Payment Terms:</span>
                  <span class="text-gray-900">{{ invoice.paymentTerms }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Items Table -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Items</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="item in (invoice.items || [])" :key="item._id || item.description" class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                      <div class="text-sm font-medium text-gray-900">{{ item.description || 'No description' }}</div>
                      <div v-if="item.type" class="text-xs text-gray-500 capitalize">{{ item.type }}</div>
                    </td>
                    <td class="px-6 py-4 text-center text-sm text-gray-900">
                      {{ item.quantity || 0 }}
                    </td>
                    <td class="px-6 py-4 text-center text-sm text-gray-900">
                      ₹{{ formatCurrency(item.unitPrice || 0) }}
                    </td>
                    <td class="px-6 py-4 text-right text-sm font-medium text-gray-900">
                      ₹{{ formatCurrency(item.total || ((item.quantity || 0) * (item.unitPrice || 0))) }}
                    </td>
                  </tr>
                  <!-- Empty state for items -->
                  <tr v-if="!invoice.items || invoice.items.length === 0">
                    <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                      <div class="flex flex-col items-center">
                        <Icon name="lucide:package" class="w-8 h-8 text-gray-300 mb-2"/>
                        <p class="text-sm">No items found in this invoice</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Totals -->
          <div class="flex justify-end">
            <div class="w-full max-w-sm">
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Subtotal:</span>
                  <span class="text-gray-900">₹{{ formatCurrency(invoice.subtotal || 0) }}</span>
                </div>
                <div v-if="(invoice.discountRate || 0) > 0" class="flex justify-between text-sm">
                  <span class="text-gray-600">Discount ({{ invoice.discountRate || 0 }}%):</span>
                  <span class="text-red-600">-₹{{ formatCurrency(invoice.discountAmount || 0) }}</span>
                </div>
                <div v-if="(invoice.taxRate || 0) > 0" class="flex justify-between text-sm">
                  <span class="text-gray-600">Tax ({{ invoice.taxRate || 0 }}%):</span>
                  <span class="text-gray-900">₹{{ formatCurrency(invoice.taxAmount || 0) }}</span>
                </div>
                <div class="border-t pt-2">
                  <div class="flex justify-between text-lg font-bold">
                    <span class="text-gray-900">Total:</span>
                    <span class="text-gray-900">₹{{ formatCurrency(invoice.total || 0) }}</span>
                  </div>
                  <!-- Debug info (remove in production) -->
                  <div v-if="!invoice.total && invoice.total !== 0" class="text-xs text-red-500 mt-2">
                    ⚠️ Total amount not calculated properly
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div v-if="invoice.notes" class="mt-8 pt-8 border-t border-gray-200">
            <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Notes</h3>
            <p class="text-gray-700 whitespace-pre-wrap">{{ invoice.notes }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Modal -->
    <EmailModal
      v-if="showEmailModal"
      :invoice="invoice"
      @close="showEmailModal = false"
      @send="handleEmailSend"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { useInvoicesStore } from '~/stores/invoices'
import type { Invoice } from '~/services/invoiceApi'

definePageMeta({
  middleware: 'auth'
})

const route = useRoute()
const router = useRouter()
const toast = useToast()
const invoicesStore = useInvoicesStore()

// State
const invoice = ref<Invoice | null>(null)
const isLoading = ref(false)
const isProcessing = ref(false)
const error = ref<string | null>(null)
const showEmailModal = ref(false)

// Computed
const isOverdue = computed(() => {
  if (!invoice.value || invoice.value.isPaid) return false
  const currentDate = new Date()
  const dueDate = new Date(invoice.value.dueDate)
  return currentDate > dueDate
})

// Methods
const loadInvoice = async () => {
  try {
    isLoading.value = true
    error.value = null

    const invoiceId = route.params.id as string
    console.log('🔍 Loading invoice with ID:', invoiceId)

    if (!invoiceId) {
      throw new Error('Invoice ID is required')
    }

    const response = await invoicesStore.fetchInvoice(invoiceId)
    console.log('📄 Invoice data received:', response)

    if (!response) {
      throw new Error('Invoice not found')
    }

    invoice.value = response

    // Validate that we have the required data
    if (!invoice.value.clientName || !invoice.value.clientEmail) {
      console.warn('⚠️ Invoice missing client data:', {
        clientName: invoice.value.clientName,
        clientEmail: invoice.value.clientEmail
      })
    }

    if (!invoice.value.total && invoice.value.total !== 0) {
      console.warn('⚠️ Invoice missing total amount:', invoice.value.total)
    }

    console.log('✅ Invoice loaded successfully:', {
      id: invoice.value._id,
      number: invoice.value.invoiceNumber,
      client: invoice.value.clientName,
      total: invoice.value.total,
      itemsCount: invoice.value.items?.length || 0
    })

  } catch (err: any) {
    error.value = err.message || 'Failed to load invoice'
    console.error('❌ Error loading invoice:', err)
  } finally {
    isLoading.value = false
  }
}

const handleEdit = () => {
  if (!invoice.value) return
  const invoiceType = invoice.value.invoiceType || 'product'
  router.push(`/invoices/edit/${invoiceType}/${invoice.value._id}`)
}

const handleMarkAsPaid = async () => {
  if (!invoice.value || isProcessing.value) return

  const previousStatus = invoice.value.status
  const previousPaidStatus = invoice.value.isPaid

  try {
    isProcessing.value = true
    const newPaidStatus = !invoice.value.isPaid

    console.log(`💰 Updating payment status: ${invoice.value.invoiceNumber} - ${newPaidStatus ? 'PAID' : 'UNPAID'}`)

    // Call backend API
    const updatedInvoice = await invoicesStore.updatePaymentStatus(invoice.value._id, newPaidStatus)

    // Update local state with backend response
    if (updatedInvoice) {
      invoice.value.isPaid = updatedInvoice.isPaid
      invoice.value.status = updatedInvoice.status

      console.log(`✅ Payment status updated successfully: isPaid=${updatedInvoice.isPaid}, status=${updatedInvoice.status}`)
    }

    // Show success message with status details
    const statusText = newPaidStatus ? 'paid' : 'unpaid'
    toast.success(`Invoice marked as ${statusText} successfully! Status updated to: ${invoice.value.status}`)

    // Refresh the invoice data to ensure consistency
    await invoicesStore.fetchInvoice(invoice.value._id)

  } catch (err: any) {
    console.error('Payment status update failed:', err)

    // Revert local state on error
    if (invoice.value) {
      invoice.value.isPaid = previousPaidStatus
      invoice.value.status = previousStatus
    }

    toast.error(err.message || 'Failed to update payment status. Please try again.')
  } finally {
    isProcessing.value = false
  }
}

const handleDownloadPDF = async () => {
  if (!invoice.value) return

  try {
    isProcessing.value = true
    const result = await invoicesStore.generatePDF(invoice.value._id)

    if (result && result.success) {
      toast.success(`PDF download started! Check your Downloads folder for ${invoice.value.invoiceNumber}.pdf`)
    } else {
      toast.success('PDF download started!')
    }
  } catch (err: any) {
    toast.error(err.message || 'Failed to generate PDF')
  } finally {
    isProcessing.value = false
  }
}

const handlePrintInvoice = async () => {
  if (!invoice.value) return

  try {
    isProcessing.value = true
    await invoicesStore.printInvoice(invoice.value._id)
    toast.success('Print preview opened!')
  } catch (err: any) {
    toast.error(err.message || 'Failed to open print preview')
  } finally {
    isProcessing.value = false
  }
}

const handleSendEmail = () => {
  showEmailModal.value = true
}

const handleEmailSend = async (emailData: { subject: string; message: string; recipientEmail: string }) => {
  if (!invoice.value) return

  try {
    isProcessing.value = true
    await invoicesStore.sendEmail(invoice.value._id, emailData)
    showEmailModal.value = false
    toast.success('Invoice email sent successfully!')
  } catch (err: any) {
    toast.error(err.message || 'Failed to send email')
  } finally {
    isProcessing.value = false
  }
}

const handleDelete = async () => {
  if (!invoice.value) return
  
  if (confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
    try {
      await invoicesStore.deleteInvoice(invoice.value._id)
      toast.success('Invoice deleted successfully!')
      router.push('/invoices')
    } catch (err: any) {
      toast.error(err.message || 'Failed to delete invoice')
    }
  }
}

// Helper functions
const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return amount.toFixed(2)
}

const getDisplayStatus = () => {
  if (!invoice.value) return 'draft'

  // If paid, always show paid status
  if (invoice.value.isPaid) {
    return 'paid'
  }

  // For draft and cancelled, show as-is
  if (invoice.value.status === 'draft' || invoice.value.status === 'cancelled') {
    return invoice.value.status
  }

  // For other statuses, show the current status (overdue will be shown as separate badge)
  return invoice.value.status === 'overdue' ? 'sent' : invoice.value.status
}

const getStatusClass = (status: string) => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    sent: 'bg-blue-100 text-blue-800',
    paid: 'bg-green-100 text-green-800',
    overdue: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || classes.draft
}

const getStatusIcon = (status: string) => {
  const icons = {
    draft: 'lucide:file-text',
    sent: 'lucide:send',
    paid: 'lucide:check-circle',
    overdue: 'lucide:clock',
    cancelled: 'lucide:x-circle'
  }
  return icons[status as keyof typeof icons] || icons.draft
}

const getStatusText = (status: string) => {
  const texts = {
    draft: 'Draft',
    sent: 'Sent',
    paid: 'Paid',
    overdue: 'Overdue',
    cancelled: 'Cancelled'
  }
  return texts[status as keyof typeof texts] || 'Unknown'
}

const generatePrintableHTML = () => {
  if (!invoice.value) return ''
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Invoice ${invoice.value.invoiceNumber}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .invoice-details { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .items-table th { background-color: #f2f2f2; }
        .totals { text-align: right; }
        .total-line { margin: 5px 0; }
        .total-final { font-weight: bold; font-size: 18px; border-top: 2px solid #000; padding-top: 10px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>INVOICE</h1>
        <h2>${invoice.value.invoiceNumber}</h2>
      </div>
      
      <div class="invoice-details">
        <div>
          <h3>Bill To:</h3>
          <p><strong>${invoice.value.clientName}</strong></p>
          <p>${invoice.value.clientEmail}</p>
          ${invoice.value.clientPhone ? `<p>${invoice.value.clientPhone}</p>` : ''}
        </div>
        <div>
          <p><strong>Issue Date:</strong> ${formatDate(invoice.value.issueDate)}</p>
          <p><strong>Due Date:</strong> ${formatDate(invoice.value.dueDate)}</p>
          <p><strong>Status:</strong> ${getStatusText(invoice.value.status)}</p>
        </div>
      </div>
      
      <table class="items-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${invoice.value.items.map((item: any) => `
            <tr>
              <td>${item.description}</td>
              <td>${item.quantity}</td>
              <td>₹${formatCurrency(item.unitPrice)}</td>
              <td>₹${formatCurrency(item.total || (item.quantity * item.unitPrice))}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div class="totals">
        <div class="total-line">Subtotal: ₹${formatCurrency(invoice.value.subtotal)}</div>
        ${invoice.value.discountRate > 0 ? `<div class="total-line">Discount (${invoice.value.discountRate}%): -₹${formatCurrency(invoice.value.discountAmount)}</div>` : ''}
        ${invoice.value.taxRate > 0 ? `<div class="total-line">Tax (${invoice.value.taxRate}%): ₹${formatCurrency(invoice.value.taxAmount)}</div>` : ''}
        <div class="total-final">Total: ₹${formatCurrency(invoice.value.total)}</div>
      </div>
      
      ${invoice.value.notes ? `
        <div style="margin-top: 30px;">
          <h3>Notes:</h3>
          <p>${invoice.value.notes}</p>
        </div>
      ` : ''}
    </body>
    </html>
  `
}

// Load invoice on mount
onMounted(() => {
  loadInvoice()
})
</script>
