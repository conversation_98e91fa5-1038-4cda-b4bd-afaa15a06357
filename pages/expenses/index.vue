<template>
    <div>
      <!-- Heading -->
      <Heading
    title="Manage Expenses"
    description="Track and manage your business expenses"
    icon="lucide:dollar-sign"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'View All Expenses',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/expenses/viewallexpenses'
      },
      {
        label: 'New Expenses',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/expenses/createexpenses'
      },
    ]"
  />
      <Expenses/>
    </div>
  </template>
  
  <script lang="ts" setup>
  import type { ChatCompletionRequestMessage } from '~/utils/types';
  
  const prompt = ref('');
  const isLoading = ref(false);
  const messages = ref<ChatCompletionRequestMessage[]>([]);
  
  const submitForm = async () => {
    if (!prompt.value.trim()) return;
  
    isLoading.value = true;
  
    const userMessage: ChatCompletionRequestMessage = {
      role: 'user',
      content: prompt.value,
    };
  
    messages.value.push(userMessage);
    const userPrompt = prompt.value;
    prompt.value = '';
  
    // Simulate bot response after a delay
    setTimeout(() => {
      const botMessage: ChatCompletionRequestMessage = {
        role: 'assistant',
        content: `You asked: "${userPrompt}". Here's a simple explanation:\nTo calculate radius, use the formula:\nradius = diameter / 2.`,
      };
  
      messages.value.push(botMessage);
      isLoading.value = false;
    }, 1000);
  };
  </script>
  
  <style>
  /* Add any custom styles if needed */
  </style>
  