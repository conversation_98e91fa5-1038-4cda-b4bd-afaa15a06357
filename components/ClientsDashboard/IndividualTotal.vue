<template>
   <!-- Individual Clients Card -->
   <div class="bg-white text-black rounded-2xl p-6 flex justify-between items-center shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
        <div class="space-y-2">
          <h2 class="text-gray-600 text-sm font-semibold">Individual Clients</h2>
          <div class="text-3xl font-bold text-purple-600">{{ individualClientsCount }}</div>
          <p class="text-sm text-gray-400">Active Individual</p>
        </div>
        <div class="p-3 bg-purple-100 rounded-xl">
            <Icon name="lucide:user-check" class="w-8 h-8 text-purple-600"/>
        </div>
      </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useClientStore } from '~/stores/clients'

const clientStore = useClientStore()

const individualClientsCount = computed(() => {
  return clientStore.clients.filter(client => client.type === 'Individual' || !client.type).length
})
</script>

<style>

</style>