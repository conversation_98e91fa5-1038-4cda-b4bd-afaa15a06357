<template>

  <div class="p-6 space-y-6">
    <!-- Filters -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      <div class="p-5 bg-white shadow rounded-lg border space-y-1">
        <label class="text-sm text-gray-600">Client</label>
        <select
          v-model="selectedClient"
          class="w-full px-4 py-2 mt-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
        >
          <option v-for="option in clients" :key="option" :value="option">{{ option }}</option>
        </select>
      </div>

      <div class="p-5 bg-white shadow rounded-lg border space-y-1">
        <label class="text-sm text-gray-600">Date Range</label>
        <select
          v-model="selectedDateRange"
          class="w-full px-4 py-2 mt-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
        >
          <option v-for="option in dateRanges" :key="option" :value="option">{{ option }}</option>
        </select>
      </div>

      <div class="p-5 bg-white shadow rounded-lg border space-y-1">
        <label class="text-sm text-gray-600">Report Type</label>
        <select
          v-model="selectedReportType"
          class="w-full px-4 py-2 mt-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
        >
          <option v-for="option in reportTypes" :key="option" :value="option">{{ option }}</option>
        </select>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <p class="text-red-600">{{ error }}</p>
      <button
        @click="fetchReports"
        class="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
      >
        Retry
      </button>
    </div>

    <!-- Summary Cards -->
    <div v-else-if="reportsData" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="p-5 bg-white shadow rounded-lg border flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500">Total Revenue</p>
          <p class="text-2xl font-bold text-green-600">${{ (summary.totalRevenue || 0).toLocaleString() }}</p>
          <p class="text-xs text-green-500 mt-1">{{ selectedDateRange }}</p>
        </div>
        <DollarSignIcon class="w-6 h-6 text-green-600" />
      </div>
      <div class="p-5 bg-white shadow rounded-lg border flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500">Total Invoices</p>
          <p class="text-2xl font-bold text-blue-600">{{ summary.totalInvoices || 0 }}</p>
          <p class="text-xs text-blue-500 mt-1">{{ selectedDateRange }}</p>
        </div>
        <TrendingUpIcon class="w-6 h-6 text-blue-600" />
      </div>
      <div class="p-5 bg-white shadow rounded-lg border flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500">Avg Invoice Value</p>
          <p class="text-2xl font-bold text-purple-600">${{ (summary.avgInvoiceValue || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</p>
          <p class="text-xs text-purple-500 mt-1">{{ selectedDateRange }}</p>
        </div>
        <BarChartIcon class="w-6 h-6 text-purple-600" />
      </div>
      <div class="p-5 bg-white shadow rounded-lg border flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500">New Clients</p>
          <p class="text-2xl font-bold text-orange-500">{{ summary.newClients || 0 }}</p>
          <p class="text-xs text-orange-500 mt-1">{{ selectedDateRange }}</p>
        </div>
        <CalendarIcon class="w-6 h-6 text-orange-500" />
      </div>
    </div>

    <!-- Top Clients and Chart -->
    <div v-if="!isLoading && !error && reportsData" class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <div class="bg-white p-4 rounded-xl shadow border">
        <h2 class="text-lg font-semibold mb-4">Top Clients by Revenue</h2>
        <div v-if="topClients.length === 0" class="text-center py-8 text-gray-500">
          No client data available
        </div>
        <div
          v-else
          v-for="(client, index) in topClients"
          :key="client.name"
          class="flex justify-between items-center py-3 border-b last:border-none"
        >
          <div class="flex items-center space-x-3">
            <div class="bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold">
              {{ index + 1 }}
            </div>
            <div>
              <p class="font-semibold">{{ client.name }}</p>
              <p class="text-sm text-gray-500 capitalize">{{ client.type }}</p>
            </div>
          </div>
          <div class="text-right">
            <p class="font-medium">${{ client.revenue.toLocaleString() }}</p>
            <p class="text-sm text-gray-500">{{ client.invoices }} invoices</p>
          </div>
        </div>
      </div>

      <div class="bg-white border p-4 rounded-xl shadow flex items-center justify-center min-h-[220px]">
        <div class="text-center space-y-2">
          <TrendingUpIcon class="w-10 h-10 text-blue-600 mx-auto" />
          <p class="text-gray-600">Chart visualization would go here</p>
          <p class="text-sm text-gray-400">Revenue trends over {{ selectedDateRange.toLowerCase() }}</p>
        </div>
      </div>
    </div>

    <!-- Detailed Client Report -->
    <div v-if="!isLoading && !error && reportsData" class="bg-white p-4 rounded-xl shadow border">
      <h2 class="text-lg font-semibold mb-4">Detailed Client Report</h2>
      <div v-if="detailedClients.length === 0" class="text-center py-8 text-gray-500">
        No client data available for the selected filters
      </div>
      <div v-else class="overflow-x-auto">
        <table class="w-full table-auto text-left">
          <thead class="text-gray-600 border-b">
            <tr>
              <th class="py-2 px-2">Client</th>
              <th class="py-2 px-2">Type</th>
              <th class="py-2 px-2">Total Invoices</th>
              <th class="py-2 px-2">Total Revenue</th>
              <th class="py-2 px-2">Avg Invoice</th>
              <th class="py-2 px-2">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="client in detailedClients"
              :key="client.email"
              class="border-b last:border-none hover:bg-gray-50"
            >
              <td class="py-3 px-2">
                <p class="font-semibold">{{ client.name }}</p>
                <p class="text-sm text-gray-500">{{ client.email }}</p>
              </td>
              <td class="px-2">
                <span
                  :class="[
                    'px-3 py-1 text-xs font-medium rounded-full',
                    client.type === 'Individual' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'
                  ]"
                >
                  {{ client.type }}
                </span>
              </td>
              <td class="px-2 font-medium">{{ client.invoices }}</td>
              <td class="px-2 text-green-600 font-semibold">${{ client.revenue.toLocaleString() }}</td>
              <td class="px-2">${{ client.avgInvoice.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</td>
              <td class="px-2">
                <span
                  :class="[
                    'px-3 py-1 text-xs font-medium rounded-full',
                    client.status === 'Active' ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-700'
                  ]"
                >
                  {{ client.status }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { DollarSignIcon, TrendingUpIcon, BarChartIcon, CalendarIcon } from 'lucide-vue-next'
import { reportsApi } from '~/services/reportsApi'

// Reactive data
const isLoading = ref(false)
const error = ref(null)
const reportsData = ref(null)

const dateRanges = ['This Month', 'Last Month', 'This Year']
const reportTypes = ['Revenue Analysis', 'Invoice Analysis', 'Client Performance']

const selectedClient = ref('All Clients')
const selectedDateRange = ref(dateRanges[0])
const selectedReportType = ref(reportTypes[0])

// Computed properties for data
const clients = computed(() => {
  if (!reportsData.value?.clients) return ['All Clients']
  return ['All Clients', ...reportsData.value.clients.map(c => c.name)]
})

const summary = computed(() => {
  return reportsData.value?.summary || {
    totalRevenue: 0,
    totalInvoices: 0,
    avgInvoiceValue: 0,
    newClients: 0
  }
})

const topClients = computed(() => {
  return reportsData.value?.topClients || []
})

const detailedClients = computed(() => {
  return reportsData.value?.detailedClients || []
})

// Fetch reports data
const fetchReports = async () => {
  try {
    isLoading.value = true
    error.value = null

    const selectedClientId = selectedClient.value === 'All Clients'
      ? undefined
      : reportsData.value?.clients?.find(c => c.name === selectedClient.value)?._id

    // Try to use authenticated endpoint first, fallback to test endpoint
    let response
    try {
      response = await reportsApi.getClientReports({
        dateRange: selectedDateRange.value,
        clientId: selectedClientId,
        reportType: selectedReportType.value
      })
    } catch (authError) {
      console.warn('Authentication failed, using test endpoint:', authError.message)
      // Fallback to test endpoint if authentication fails
      response = await reportsApi.getClientReportsTest({
        dateRange: selectedDateRange.value,
        clientId: selectedClientId,
        reportType: selectedReportType.value
      })
    }

    if (response.success) {
      reportsData.value = response.data
    } else {
      error.value = response.message || 'Failed to fetch reports data'
    }
  } catch (err) {
    console.error('Error fetching reports:', err)
    error.value = err.message || 'Failed to fetch reports data'
  } finally {
    isLoading.value = false
  }
}

// Watch for filter changes
watch([selectedClient, selectedDateRange, selectedReportType], () => {
  fetchReports()
})

// Initial load
onMounted(() => {
  fetchReports()
})
</script>
