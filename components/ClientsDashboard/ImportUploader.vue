<template>
  
  <div class="p-6 mx-auto space-y-8">
    <!-- Choose Import Method -->
    <div class="bg-white p-10 border rounded-lg shadow-lg">
      <h2 class="text-xl font-semibold mb-4">Choose Import Method</h2>
      <div class="flex flex-col sm:flex-row gap-4">
        <div
          :class="[
            'flex-1 border rounded-lg p-8 cursor-pointer flex items-center gap-4',
            selected === 'excel' ? 'bg-blue-50 border-blue-500' : 'border-gray-300',
          ]"
          @click="selected = 'excel'"
        >
          <FileSpreadsheet class="text-green-600 w-8 h-8" />
          <div>
            <h3 class="font-bold">Excel File</h3>
            <p class="text-sm text-gray-500">Upload .xlsx, .xls, or .csv files</p>
          </div>
        </div>
        <div
          :class="[
            'flex-1 border rounded-lg p-8 cursor-pointer flex items-center gap-4',
            selected === 'google' ? 'bg-blue-50 border-blue-500' : 'border-gray-300',
          ]"
          @click="selected = 'google'"
        >
          <FileText class="text-blue-600 w-8 h-8" />
          <div>
            <h3 class="font-bold">Google Sheets</h3>
            <p class="text-sm text-gray-500">Import directly from Google Sheets</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Upload File -->
    <div class="bg-white shadow-lg p-10 border rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Upload File</h2>
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg h-52 flex flex-col items-center justify-center text-center p-6"
      >
        <Upload class="w-10 h-10 text-gray-500 mb-2" />
        <p class="text-gray-700 font-medium">Drop your file here</p>
        <p class="text-sm text-gray-500">or click to browse</p>
        <label
          class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded cursor-pointer"
        >
          Browse Files
          <input type="file" class="hidden" />
        </label>
        <p class="mt-2 text-sm text-gray-400">Supported formats: .xlsx, .xls, .csv (Max 10MB)</p>
      </div>
    </div>

    <!-- Import Instructions -->
    <div class="bg-white border rounded-lg p-10 shadow-lg">
      <h2 class="text-xl font-semibold mb-4">Import Instructions</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm">
        <div>
          <h3 class="font-semibold mb-2">Required Fields</h3>
          <ul class="list-disc list-inside space-y-1 text-gray-700">
            <li>Name (required)</li>
            <li>Type (business/individual)</li>
            <li>Email (required)</li>
            <li>Phone</li>
          </ul>
        </div>
        <div>
          <h3 class="font-semibold mb-2">Optional Fields</h3>
          <ul class="list-disc list-inside space-y-1 text-gray-700">
            <li>Address, City, State, ZipCode</li>
            <li>Company Name, Contact Person</li>
            <li>Tax ID, Website</li>
            <li>First Name, Last Name (for individuals)</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { FileSpreadsheet, FileText, Upload } from 'lucide-vue-next'

const selected = ref('excel')
</script>
