<script setup lang="ts">
import { clientApi, type Client } from '~/services/clientApi'
import { useToast } from 'vue-toastification'

// Props
interface Props {
  clientId: string
}

const props = defineProps<Props>()
const toast = useToast()

// State
const client = ref<Client | null>(null)
const isLoading = ref(false)
const error = ref('')

const tabs = [
  { name: 'Overview', key: 'overview' },
  { name: 'Invoices', key: 'invoices' },
  { name: 'Analytics', key: 'analytics' },
  { name: 'Documents', key: 'documents' }
]

const selectedTab = ref('overview')

// Computed properties
const clientInitial = computed(() => {
  if (!client.value) return 'C'
  const name = client.value.company?.name || client.value.name
  return name?.[0]?.toUpperCase() || 'C'
})

const displayName = computed(() => {
  if (!client.value) return ''
  return client.value.company?.name || client.value.name
})

// Methods
const fetchClient = async () => {
  if (!props.clientId) return

  try {
    isLoading.value = true
    error.value = ''

    console.log('🔍 Fetching client with ID:', props.clientId)

    const response = await clientApi.getClient(props.clientId)
    client.value = response.data

    console.log('✅ Client loaded successfully:', client.value)

  } catch (err: any) {
    error.value = err.message || 'Failed to load client'
    console.error('❌ Error loading client:', err)
    toast.error(`Failed to load client: ${err.message}`)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchClient()
})

// Watch for clientId changes
watch(() => props.clientId, () => {
  fetchClient()
}, { immediate: false })

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount || 0)
}

const formatDate = (date: string | undefined) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<template>
  <Heading
      title="Manage Clients"
      description="Manage your clients and their details"
      icon="lucide:users"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'Back to Clients',
          icon: 'lucide:arrow-left',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          to: '/clients'
        },
      ]"
    />
  <div class="p-6 space-y-6 max-w-5xl mx-auto">

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      <span class="ml-2 text-gray-600">Loading client details...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-500 mb-4">
        <Icon name="lucide:alert-circle" class="w-12 h-12 mx-auto mb-2" />
        <p class="text-lg font-semibold">Failed to load client</p>
        <p class="text-sm">{{ error }}</p>
      </div>
      <Button @click="fetchClient" variant="outline">
        <Icon name="lucide:refresh-cw" class="w-4 h-4 mr-2" />
        Try Again
      </Button>
    </div>

    <!-- Client Details -->
    <div v-else-if="client">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div
            class="w-14 h-14 rounded-full bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center text-white text-xl font-bold"
          >
            {{ clientInitial }}
          </div>
          <div>
            <h2 class="text-xl font-semibold">{{ displayName }}</h2>
            <div class="flex items-center gap-2 mt-1">
              <Badge variant="secondary">{{ client.type }}</Badge>
              <Badge variant="success">{{ client.status }}</Badge>
            </div>
          </div>
        </div>
        <Button variant="outline">Edit</Button>
      </div>

    <!-- Tabs -->
    <Tabs v-model="selectedTab" class="w-full">
      <TabsList class="grid grid-cols-4 w-full border-b mb-4">
        <TabsTrigger
          v-for="tab in tabs"
          :key="tab.key"
          :value="tab.key"
          class="py-2 px-4 text-sm font-medium"
        >
          {{ tab.name }}
        </TabsTrigger>
      </TabsList>

      <!-- Overview Tab -->
      <TabsContent value="overview">
        <div class="space-y-6">
          <!-- Stats -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card class="bg-blue-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Total Invoices</p>
                <p class="text-2xl font-bold">{{ client.totalInvoices || 0 }}</p>
              </CardContent>
            </Card>
            <Card class="bg-green-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Paid</p>
                <p class="text-2xl font-bold">1</p>
              </CardContent>
            </Card>
            <Card class="bg-yellow-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Pending</p>
                <p class="text-2xl font-bold">1</p>
              </CardContent>
            </Card>
            <Card class="bg-red-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Overdue</p>
                <p class="text-2xl font-bold">1</p>
              </CardContent>
            </Card>
            <Card class="bg-orange-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Total Amount</p>
                <p class="text-2xl font-bold text-orange-600">₹{{ formatCurrency(client.totalAmount || 0) }}</p>
              </CardContent>
            </Card>
          </div>

          <!-- Info Sections -->
          <div class="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent class="space-y-2">
                <p><strong>Client ID:</strong> {{ client.clientId || 'Generating...' }}</p>
                <p><strong>Email:</strong> {{ client.email }}</p>
                <p><strong>Phone:</strong> {{ client.phone || 'Not provided' }}</p>
                <p><strong>Company:</strong> {{ client.company?.name || 'N/A' }}</p>
                <p><strong>Tax ID:</strong> {{ client.company?.taxId || 'N/A' }}</p>
                <p><strong>Created:</strong> {{ formatDate(client.createdAt) }}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Financial Summary</CardTitle>
              </CardHeader>
              <CardContent class="space-y-2">
                <p>Total Revenue: ₹{{ formatCurrency(client.totalAmount || 0) }}</p>
                <p>Outstanding: <span class="text-red-600">₹{{ formatCurrency(client.outstandingAmount || 0) }}</span></p>
                <p>Total Invoices: <span class="text-blue-600">{{ client.totalInvoices || 0 }}</span></p>
                <p>Payment Terms: {{ client.paymentTerms || 'Net 30' }}</p>
                <p>Currency: {{ client.currency || 'USD' }}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent class="flex flex-col gap-2">
                <Button variant="outline">Create Invoice</Button>
                <Button variant="outline">Send Email</Button>
                <Button variant="outline">Export Data</Button>
                <Button variant="outline">Record Payment</Button>
              </CardContent>
            </Card>
          </div>

          <!-- Recent Activity -->
          <div>
            <h3 class="font-semibold text-lg">Recent Activity</h3>
            <p class="mt-2 text-gray-500">No recent activity.</p>
          </div>
        </div>
      </TabsContent>

      <!-- Other Tabs -->
      <TabsContent
        v-for="tab in tabs.filter(t => t.key !== 'overview')"
        :key="tab.key"
        :value="tab.key"
      >
        <Card>
          <CardContent class="py-6 text-center text-gray-500">
            {{ tab.name }} content goes here...
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
    </div>
  </div>
</template>
