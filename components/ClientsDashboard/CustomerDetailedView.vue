<script setup lang="ts">
import { ref, computed } from 'vue'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'

const client = ref({
  companyName: 'Acme Corp',
  type: 'Business',
  email: '<EMAIL>',
  phone: '+91 98765 43210',
  gstNumber: '27AABCU9603R1ZX',
  invoices: 4,
  totalBilled: 35000
})

const tabs = [
  { name: 'Overview', key: 'overview' },
  { name: 'Invoices', key: 'invoices' },
  { name: 'Analytics', key: 'analytics' },
  { name: 'Documents', key: 'documents' }
]

const selectedTab = ref('overview')

const clientInitial = computed(() =>
  client.value.companyName?.[0]?.toUpperCase() || 'C'
)
</script>

<template>
  <Heading
      title="Manage Clients"
      description="Manage your clients and their details"
      icon="lucide:users"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'Back to Clients',
          icon: 'lucide:arrow-left',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          to: '/clients'
        },
      ]"
    />
  <div class="p-6 space-y-6 max-w-5xl mx-auto">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div
          class="w-14 h-14 rounded-full bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center text-white text-xl font-bold"
        >
          {{ clientInitial }}
        </div>
        <div>
          <h2 class="text-xl font-semibold">{{ client.companyName }}</h2>
          <div class="flex items-center gap-2 mt-1">
            <Badge variant="secondary">{{ client.type }}</Badge>
            <Badge variant="success">Active</Badge>
          </div>
        </div>
      </div>
      <Button variant="outline">Edit</Button>
    </div>

    <!-- Tabs -->
    <Tabs v-model="selectedTab" class="w-full">
      <TabsList class="grid grid-cols-4 w-full border-b mb-4">
        <TabsTrigger
          v-for="tab in tabs"
          :key="tab.key"
          :value="tab.key"
          class="py-2 px-4 text-sm font-medium"
        >
          {{ tab.name }}
        </TabsTrigger>
      </TabsList>

      <!-- Overview Tab -->
      <TabsContent value="overview">
        <div class="space-y-6">
          <!-- Stats -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card class="bg-blue-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Total Invoices</p>
                <p class="text-2xl font-bold">{{ client.invoices }}</p>
              </CardContent>
            </Card>
            <Card class="bg-green-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Paid</p>
                <p class="text-2xl font-bold">1</p>
              </CardContent>
            </Card>
            <Card class="bg-yellow-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Pending</p>
                <p class="text-2xl font-bold">1</p>
              </CardContent>
            </Card>
            <Card class="bg-red-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Overdue</p>
                <p class="text-2xl font-bold">1</p>
              </CardContent>
            </Card>
            <Card class="bg-orange-50 text-center">
              <CardContent>
                <p class="text-sm text-gray-500">Total Due</p>
                <p class="text-2xl font-bold text-orange-600">₹{{ client.totalBilled }}</p>
              </CardContent>
            </Card>
          </div>

          <!-- Info Sections -->
          <div class="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent class="space-y-2">
                <p><strong>Email:</strong> {{ client.email }}</p>
                <p><strong>Phone:</strong> {{ client.phone }}</p>
                <p><strong>Company:</strong> {{ client.companyName }}</p>
                <p><strong>GST:</strong> {{ client.gstNumber }}</p>
                <p><strong>Created:</strong> 6/15/1985</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Financial Summary</CardTitle>
              </CardHeader>
              <CardContent class="space-y-2">
                <p>Total Revenue: ₹125,000</p>
                <p>Outstanding: <span class="text-red-600">₹25,000</span></p>
                <p>Total Due: <span class="text-orange-600">₹{{ client.totalBilled }}</span></p>
                <p>Product Revenue: ₹75,000</p>
                <p>Service Revenue: ₹50,000</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent class="flex flex-col gap-2">
                <Button variant="outline">Create Invoice</Button>
                <Button variant="outline">Send Email</Button>
                <Button variant="outline">Export Data</Button>
                <Button variant="outline">Record Payment</Button>
              </CardContent>
            </Card>
          </div>

          <!-- Recent Activity -->
          <div>
            <h3 class="font-semibold text-lg">Recent Activity</h3>
            <p class="mt-2 text-gray-500">No recent activity.</p>
          </div>
        </div>
      </TabsContent>

      <!-- Other Tabs -->
      <TabsContent
        v-for="tab in tabs.filter(t => t.key !== 'overview')"
        :key="tab.key"
        :value="tab.key"
      >
        <Card>
          <CardContent class="py-6 text-center text-gray-500">
            {{ tab.name }} content goes here...
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
