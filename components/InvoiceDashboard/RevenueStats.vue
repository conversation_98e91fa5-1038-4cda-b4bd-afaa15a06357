<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
    <!-- Total Revenue -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Total Revenue</p>
        <div v-if="isLoading" class="animate-pulse">
          <div class="h-8 bg-gray-200 rounded w-24"></div>
        </div>
        <p v-else class="text-2xl font-bold">₹{{ formatCurrency(stats.totalAmount) }}</p>
      </div>
      <CheckCircle class="w-7 h-7 text-green-500" />
    </div>

    <!-- Paid -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Paid</p>
        <div v-if="isLoading" class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-8"></div>
        </div>
        <div v-else>
          <p class="text-xl font-semibold text-green-600">{{ stats.paidCount }}</p>
          <p class="text-sm text-gray-400">₹{{ formatCurrency(stats.paidAmount) }}</p>
        </div>
      </div>
      <CheckCircle class="w-7 h-7 text-green-500" />
    </div>

    <!-- Pending -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Pending</p>
        <div v-if="isLoading" class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-8"></div>
        </div>
        <div v-else>
          <p class="text-xl font-semibold text-blue-600">{{ stats.sentCount }}</p>
          <p class="text-sm text-gray-400">₹{{ formatCurrency(stats.pendingAmount) }}</p>
        </div>
      </div>
      <Clock class="w-7 h-7 text-blue-500" />
    </div>

    <!-- Overdue -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Overdue</p>
        <div v-if="isLoading" class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-8"></div>
        </div>
        <div v-else>
          <p class="text-xl font-semibold text-red-600">{{ stats.overdueCount }}</p>
          <p class="text-sm text-gray-400">₹{{ formatCurrency(stats.overdueAmount) }}</p>
        </div>
      </div>
      <AlertCircle class="w-7 h-7 text-red-500" />
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { CheckCircle, Clock, AlertCircle } from 'lucide-vue-next'
import { useInvoicesStore } from '~/stores/invoices'

const invoicesStore = useInvoicesStore()

// Computed properties
const stats = computed(() => invoicesStore.stats)
const isLoading = computed(() => invoicesStore.isLoading)

// Format currency helper
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount || 0)
}

// Load stats on component mount
onMounted(async () => {
  try {
    await invoicesStore.fetchStats()
  } catch (error) {
    console.error('Failed to load invoice stats:', error)
  }
})
</script>
