<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Send Invoice via Email</h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            <Icon name="lucide:x" class="w-5 h-5" />
          </button>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="px-6 py-4 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Recipient Email
          </label>
          <input
            v-model="form.recipientEmail"
            type="email"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="Enter recipient email"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Subject
          </label>
          <input
            v-model="form.subject"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="Email subject"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Message
          </label>
          <textarea
            v-model="form.message"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="Email message"
          ></textarea>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSending"
            class="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            <Icon v-if="isSending" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
            <Icon v-else name="lucide:send" class="w-4 h-4 mr-2" />
            {{ isSending ? 'Sending...' : 'Send Email' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { Invoice } from '~/services/invoiceApi'

interface Props {
  invoice: Invoice | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  send: [emailData: { subject: string; message: string; recipientEmail: string }]
}>()

const isSending = ref(false)

const form = reactive({
  recipientEmail: '',
  subject: '',
  message: ''
})

const handleSubmit = async () => {
  try {
    isSending.value = true
    emit('send', { ...form })
  } finally {
    isSending.value = false
  }
}

// Initialize form with default values
onMounted(() => {
  if (props.invoice) {
    form.recipientEmail = props.invoice.clientEmail
    form.subject = `Invoice ${props.invoice.invoiceNumber}`
    form.message = `Dear ${props.invoice.clientName},

Please find attached invoice ${props.invoice.invoiceNumber} for the amount of ₹${props.invoice.total.toFixed(2)}.

Due Date: ${new Date(props.invoice.dueDate).toLocaleDateString()}

Thank you for your business!

Best regards`
  }
})
</script>
