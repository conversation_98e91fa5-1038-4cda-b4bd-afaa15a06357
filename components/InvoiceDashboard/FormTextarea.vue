<template>
  <div class="flex flex-col gap-1">
    <label :for="name" class="text-sm font-medium text-foreground">
      {{ label }}
    </label>
    <textarea
      :id="name"
      :name="name"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      rows="3"
      class="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
      :class="{ 'border-destructive': error }"
    ></textarea>
    <p v-if="error" class="text-destructive text-sm flex items-center gap-1 mt-1">
      <AlertCircle class="w-4 h-4" /> {{ error }}
    </p>
  </div>
</template>

<script setup>
import { AlertCircle } from 'lucide-vue-next'

defineProps({
  modelValue: String,
  label: String,
  name: String,
  error: String
})
defineEmits(['update:modelValue'])
</script>
