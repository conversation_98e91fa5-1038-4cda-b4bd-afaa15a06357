<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4 py-10">
    <!-- Header -->
    <div class="flex items-center gap-2 mb-6 w-full max-w-md">
      <ArrowLeft class="w-5 h-5 text-gray-600 cursor-pointer" @click="$router.back()" />
      <div class="flex items-center gap-2">
        <div class="w-7 h-7 rounded-full bg-green-500 flex items-center justify-center text-white">
          <CreditCard class="w-4 h-4" />
        </div>
        <h2 class="text-lg font-semibold text-gray-800">Add Credit/Debit Card</h2>
      </div>
    </div>

    <!-- Card Form -->
    <div class="bg-white rounded-xl shadow-md w-full max-w-md p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">Card Details</h3>

      <!-- Card Number -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Card Number *</label>
        <input
          type="text"
          placeholder="1234 5678 9012 3456"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:outline-none"
        />
      </div>

      <!-- Cardholder Name -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Cardholder Name *</label>
        <input
          type="text"
          placeholder="John Doe"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:outline-none"
        />
      </div>

      <!-- Expiry + CVV -->
      <div class="flex gap-3 mb-4">
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">Month *</label>
          <input
            type="text"
            placeholder="MM"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:outline-none"
          />
        </div>
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">Year *</label>
          <input
            type="text"
            placeholder="YY"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:outline-none"
          />
        </div>
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">CVV *</label>
          <input
            type="text"
            placeholder="123"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:outline-none"
          />
        </div>
      </div>

      <!-- Nickname -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Nickname (Optional)</label>
        <input
          type="text"
          placeholder="My Primary Card"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:outline-none"
        />
      </div>

      <!-- Default Checkbox -->
      <div class="mb-6 flex items-center gap-2">
        <input id="defaultCard" type="checkbox" class="accent-green-500" />
        <label for="defaultCard" class="text-sm text-gray-700">Set as default payment method</label>
      </div>

      <!-- Buttons -->
      <div class="flex justify-between items-center gap-4">
        <button
          class="w-full py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium"
        >
          Cancel
        </button>
        <button
          class="w-full py-2 rounded-md bg-green-500 text-white font-medium text-sm hover:bg-green-600 flex items-center justify-center gap-2"
        >
          <Check class="w-4 h-4" /> Add Card
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft, CreditCard, Check } from 'lucide-vue-next'
</script>
