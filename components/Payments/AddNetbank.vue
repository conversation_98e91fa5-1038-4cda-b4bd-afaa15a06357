<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4 py-10">
    <!-- Header -->
    <div class="flex items-center gap-2 mb-6 w-full max-w-md">
      <ArrowLeft class="w-5 h-5 text-gray-600 cursor-pointer" @click="$router.back()" />
      <div class="flex items-center gap-2">
        <div class="w-7 h-7 rounded-full bg-orange-500 flex items-center justify-center text-white">
          <Banknote class="w-4 h-4" />
        </div>
        <h2 class="text-lg font-semibold text-gray-800">Add Net Banking</h2>
      </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-md w-full max-w-md p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">Bank Account Details</h3>

      <!-- Bank Name -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Bank Name *</label>
        <select
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:outline-none"
        >
          <option>Select your bank</option>
          <option>SBI</option>
          <option>HDFC</option>
          <option>ICICI</option>
          <option>Axis Bank</option>
        </select>
      </div>

      <!-- Account Holder Name -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Account Holder Name *</label>
        <input
          type="text"
          placeholder="John Doe"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:outline-none"
        />
      </div>

      <!-- Account Number -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Account Number *</label>
        <input
          type="text"
          placeholder="**********"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:outline-none"
        />
      </div>

      <!-- IFSC Code -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">IFSC Code *</label>
        <input
          type="text"
          placeholder="SBIN0001234"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:outline-none"
        />
      </div>

      <!-- Nickname -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">Nickname (Optional)</label>
        <input
          type="text"
          placeholder="My Salary Account"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:outline-none"
        />
      </div>

      <!-- Default Checkbox -->
      <div class="mb-6 flex items-center gap-2">
        <input id="defaultBank" type="checkbox" class="accent-orange-500" />
        <label for="defaultBank" class="text-sm text-gray-700">Set as default payment method</label>
      </div>

      <!-- Buttons -->
      <div class="flex justify-between items-center gap-4">
        <button
          class="w-full py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium"
        >
          Cancel
        </button>
        <button
          class="w-full py-2 rounded-md bg-orange-500 text-white font-medium text-sm hover:bg-orange-600 flex items-center justify-center gap-2"
        >
          <Check class="w-4 h-4" /> Add Bank Account
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeft, Banknote, Check } from 'lucide-vue-next'
</script>
