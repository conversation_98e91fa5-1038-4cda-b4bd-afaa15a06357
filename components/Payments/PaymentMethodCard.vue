<template>
  <div class="p-4 bg-white border rounded-xl shadow flex justify-between items-center">
    <div>
      <h3 class="font-semibold text-gray-800 flex items-center">
        <component :is="icon" class="w-4 h-4 mr-2 text-blue-600" />
        {{ title }}
        <span v-if="isDefault" class="ml-2 text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded">Default</span>
      </h3>
      <p class="text-sm text-gray-500">{{ subtitle }}</p>
      <p class="text-xs text-gray-400 mt-1">Added {{ date }}</p>
    </div>
    <div class="relative">
      <button @click="showMenu = !showMenu">
        <MoreVertical class="w-5 h-5 text-gray-500" />
      </button>
      <div v-if="showMenu" class="absolute right-0 mt-2 w-48 bg-white border rounded-lg shadow z-10">
        <ul class="text-sm text-gray-700">
          <li class="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center"><Eye class="w-4 h-4 mr-2" /> View Details</li>
          <li class="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center"><Pen class="w-4 h-4 mr-2" /> Edit</li>
          <li class="px-4 py-2 hover:bg-gray-50 cursor-pointer flex items-center"><CheckCircle class="w-4 h-4 mr-2" /> Set as Default</li>
          <li class="px-4 py-2 text-red-600 hover:bg-red-50 cursor-pointer flex items-center"><Trash class="w-4 h-4 mr-2" /> Delete</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MoreVertical, Eye, Pen, CheckCircle, Trash } from 'lucide-vue-next'
import { ref } from 'vue'

const props = defineProps({
  icon: Object,
  title: String,
  subtitle: String,
  date: String,
  isDefault: Boolean
})

const showMenu = ref(false)
</script>
