<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center px-4 py-10">
    <h1 class="text-2xl md:text-3xl font-semibold text-gray-800 mb-2">Choose Payment Method</h1>
    <p class="text-gray-500 mb-8 text-center">Select your preferred payment method to continue</p>

    <div class="grid gap-6 md:grid-cols-3 w-full max-w-5xl">
      <!-- UPI Card -->
      <div class="bg-blue-50 rounded-xl p-6 text-center shadow-sm">
        <div class="mx-auto mb-4 w-16 h-16 flex items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-blue-500 text-white">
          <Smartphone class="w-7 h-7" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800">UPI Payment</h3>
        <p class="text-sm text-gray-500 mt-1">Pay using UPI ID or QR code</p>
        <a href="/payments/addupi">
            <Button class="mt-4 w-full border text-sm rounded-lg py-2 font-medium flex items-center justify-center gap-2"
            variant="premium"
            >
          <Plus class="w-4 h-4" /> Add UPI Payment
        </Button>
        </a>
      </div>

      <!-- Credit/Debit Card -->
      <div class="bg-green-50 rounded-xl p-6 text-center shadow-sm">
        <div class="mx-auto mb-4 w-16 h-16 flex items-center justify-center rounded-full bg-green-500 text-white">
          <CreditCard class="w-7 h-7" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800">Credit/Debit Card</h3>
        <p class="text-sm text-gray-500 mt-1">Pay using Visa, Mastercard, etc.</p>
        <a href="/payments/addcard">
            <Button class="mt-4 w-full border text-sm rounded-lg py-2 font-medium flex items-center justify-center gap-2"
            variant="premium"
            >
          <Plus class="w-4 h-4" /> Add Credit/Debit Card
        </Button>
        </a>
      </div>

      <!-- Net Banking -->
      <div class="bg-orange-50 rounded-xl p-6 text-center shadow-sm">
        <div class="mx-auto mb-4 w-16 h-16 flex items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-500 text-white">
          <Building2 class="w-7 h-7" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800">Net Banking</h3>
        <p class="text-sm text-gray-500 mt-1">Pay using your bank account</p>
        <a href="/payments/addnetbank">
            <Button class="mt-4 w-full border text-sm rounded-lg py-2 font-medium flex items-center justify-center gap-2"
            variant="premium"
            >
          <Plus class="w-4 h-4" /> Add Net Banking
        </Button>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Smartphone, CreditCard, Building2, Plus } from 'lucide-vue-next'
</script>
