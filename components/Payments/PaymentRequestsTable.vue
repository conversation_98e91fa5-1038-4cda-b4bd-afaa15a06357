<template>
  <div class="p-6">
    <!-- Card -->
    <div class="bg-white shadow-md rounded-2xl p-6 border">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800">Payment Requests</h2>

        <!-- Search & Filter -->
        <div class="flex items-center gap-3">
          <div class="relative">
            <input
              v-model="search"
              type="text"
              placeholder="Search requests..."
              class="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm text-gray-600 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <Search class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
          </div>
          <select
            v-model="selectedStatus"
            class="px-3 py-2 text-sm border border-gray-200 rounded-md text-gray-700 bg-white"
          >
            <option value="">All Status</option>
            <option value="sent">Sent</option>
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
          </select>
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full text-sm text-left text-gray-700">
          <thead>
            <tr class="text-gray-500 border-b">
              <th class="py-3 px-4">Request ID</th>
              <th class="py-3 px-4">Invoice</th>
              <th class="py-3 px-4">Client</th>
              <th class="py-3 px-4">Amount</th>
              <th class="py-3 px-4">Due Date</th>
              <th class="py-3 px-4">Sent Date</th>
              <th class="py-3 px-4">Method</th>
              <th class="py-3 px-4">Status</th>
              <th class="py-3 px-4">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="request in filteredRequests"
              :key="request.id"
              class="border-b hover:bg-gray-50"
            >
              <td class="py-3 px-4 font-medium">{{ request.id }}</td>
              <td class="py-3 px-4">
                <span class="px-2 py-0.5 text-xs rounded-full border border-gray-200 bg-gray-50 font-semibold">
                  {{ request.invoice }}
                </span>
              </td>
              <td class="py-3 px-4">{{ request.client }}</td>
              <td class="py-3 px-4 font-medium">₹{{ request.amount }}</td>
              <td class="py-3 px-4 flex items-center gap-1">
                <Calendar class="w-4 h-4 text-gray-400" />
                {{ request.due }}
              </td>
              <td class="py-3 px-4">{{ request.sent }}</td>
              <td class="py-3 px-4">
                <div
                  class="inline-flex items-center gap-1 px-2 py-0.5 border rounded-full text-xs"
                >
                  <component :is="request.methodIcon" class="w-4 h-4" />
                  {{ request.method }}
                </div>
              </td>
              <td class="py-3 px-4">
                <span
                  class="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium"
                  :class="statusClass(request.status)"
                >
                  <component :is="request.statusIcon" class="w-4 h-4" />
                  {{ request.status }}
                </span>
              </td>
              <td class="py-3 px-4">
                <Eye class="w-4 h-4 text-gray-500 cursor-pointer hover:text-indigo-600" />
              </td>
            </tr>
            <tr v-if="filteredRequests.length === 0">
              <td colspan="9" class="text-center py-6 text-gray-400">No matching results found.</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Search,
  Filter,
  Eye,
  Mail,
  Calendar,
  MessageCircle,
  Clock,
  Send,
  CheckCheck,
} from 'lucide-vue-next'

const search = ref('')
const selectedStatus = ref('')

const requests = ref([
  {
    id: 'PR-001',
    invoice: 'INV-001',
    client: 'Acme Corp',
    amount: '35,000',
    due: '2024-06-15',
    sent: '2024-06-10',
    method: 'Email',
    methodIcon: Mail,
    status: 'sent',
    statusIcon: Send,
  },
  {
    id: 'PR-002',
    invoice: 'INV-002',
    client: 'Tech Solutions',
    amount: '28,000',
    due: '2024-06-20',
    sent: '2024-06-11',
    method: 'Whatsapp',
    methodIcon: MessageCircle,
    status: 'pending',
    statusIcon: Clock,
  },
  {
    id: 'PR-003',
    invoice: 'INV-003',
    client: 'StartupX',
    amount: '42,000',
    due: '2024-06-25',
    sent: '2024-06-12',
    method: 'Email',
    methodIcon: Mail,
    status: 'paid',
    statusIcon: CheckCheck,
  },
])

const filteredRequests = computed(() => {
  return requests.value.filter((req) => {
    const matchesSearch =
      req.id.toLowerCase().includes(search.value.toLowerCase()) ||
      req.invoice.toLowerCase().includes(search.value.toLowerCase()) ||
      req.client.toLowerCase().includes(search.value.toLowerCase())

    const matchesStatus =
      selectedStatus.value === '' || req.status === selectedStatus.value

    return matchesSearch && matchesStatus
  })
})

const statusClass = (status) => {
  return {
    sent: 'bg-blue-100 text-blue-600',
    pending: 'bg-orange-100 text-orange-600',
    paid: 'bg-green-100 text-green-600',
  }[status]
}
</script>
