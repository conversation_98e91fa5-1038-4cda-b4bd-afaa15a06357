<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
    <!-- Total Revenue -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Total Received</p>
        <p class="text-2xl font-bold text-gray-800">₹1,03,000</p>
      </div>
      <Wallet class="w-7 h-7 text-green-600" />
    </div>

    <!-- Pending -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Pending</p>
        <p class="text-xl font-semibold text-yellow-600">₹15,000</p>
      </div>
      <Clock class="w-7 h-7 text-yellow-500" />
    </div>

    <!-- This Month -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">This Month</p>
        <p class="text-xl font-semibold text-blue-600">₹88,000</p>
      </div>
      <Calendar class="w-7 h-7 text-blue-500" />
    </div>

    <!-- Total Payments -->
    <div class="bg-white shadow-md p-6 rounded-xl flex justify-between items-center border">
      <div>
        <p class="text-gray-500 font-medium">Total Payments</p>
        <p class="text-xl font-semibold text-indigo-600">5</p>
      </div>
      <Receipt class="w-7 h-7 text-indigo-500" />
    </div>
  </div>
</template>

<script setup>
import { Wallet, Clock, Calendar, Receipt } from 'lucide-vue-next'
</script>
