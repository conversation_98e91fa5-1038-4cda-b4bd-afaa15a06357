<template>
  <div class="p-3">
    <div class="p-6 bg-gray-50 rounded-xl shadow-md border">

      <!-- Search & Filter Row -->
      <div class="flex flex-wrap gap-4 mb-4 items-center">
        <!-- Search Input -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="search"
              type="text"
              placeholder="Search..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
          </div>
        </div>

        <!-- Mode Filter -->
        <div class="flex items-center gap-2">
          <Filter class="text-gray-500 w-4 h-4" />
          <select
            v-model="modeFilter"
            class="text-sm border border-gray-300 rounded-md px-3 py-2 focus:ring-indigo-500 focus:outline-none"
          >
            <option>All Modes</option>
            <option>upi</option>
            <option>netbanking</option>
            <option>cash</option>
            <option>card</option>
          </select>
        </div>
      </div>

      <!-- Payments Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white rounded-xl overflow-hidden">
          <thead class="text-sm text-left text-gray-600 bg-gray-100">
            <tr>
              <th class="px-4 py-3">Payment ID</th>
              <th class="px-4 py-3">Client</th>
              <th class="px-4 py-3">Date</th>
              <th class="px-4 py-3">Amount</th>
              <th class="px-4 py-3">Mode</th>
              <th class="px-4 py-3">Linked Invoice</th>
              <th class="px-4 py-3">Status</th>
              <th class="px-4 py-3">Actions</th>
            </tr>
          </thead>
          <tbody class="text-sm text-gray-800">
            <tr
              v-for="payment in filteredPayments"
              :key="payment.id"
              class="border-b hover:bg-gray-50 transition"
            >
              <td class="px-4 py-3 font-medium">{{ payment.id }}</td>
              <td class="px-4 py-3 font-semibold">{{ payment.client }}</td>
              <td class="px-4 py-3">{{ payment.date }}</td>
              <td class="px-4 py-3 font-medium">₹{{ payment.amount }}</td>
              <td class="px-4 py-3">
                <span
                  class="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium"
                  :class="modeClasses[payment.mode]"
                >
                  <component :is="payment.icon" class="w-3.5 h-3.5" />
                  {{ payment.mode }}
                </span>
              </td>
              <td class="px-4 py-3">
                <span class="text-xs font-semibold border border-gray-200 rounded-full px-2 py-0.5">
                  {{ payment.invoice }}
                </span>
              </td>
              <td class="px-4 py-3">
                <span
                  class="text-xs font-medium rounded-full px-2 py-0.5"
                  :class="statusClasses[payment.status]"
                >
                  {{ payment.status }}
                </span>
              </td>
              <td class="px-4 py-3 space-x-2 text-gray-700 flex items-center">
                <Eye class="w-4 h-4 cursor-pointer hover:text-indigo-500" />
                <Edit class="w-4 h-4 cursor-pointer hover:text-indigo-500" />
                <Download class="w-4 h-4 cursor-pointer hover:text-indigo-500" />
                <Trash class="w-4 h-4 text-red-500 cursor-pointer hover:text-red-700" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
    </div>
  </div>
</template>

<script setup>
import {
  Search,
  Filter,
  Eye,
  Edit,
  Download,
  Trash,
  CreditCard,
  IndianRupee,
  Landmark,
  Smartphone,
} from 'lucide-vue-next'

import { ref, computed } from 'vue'

// Filters
const search = ref('')
const modeFilter = ref('All Modes')

// Data
const payments = ref([
  {
    id: 'PAY-001',
    client: 'Acme Corp',
    date: '2024-05-29',
    amount: '25,000',
    mode: 'upi',
    icon: Smartphone,
    invoice: 'INV-001',
    status: 'completed',
  },
  {
    id: 'PAY-002',
    client: 'Tech Solutions',
    date: '2024-05-28',
    amount: '18,000',
    mode: 'netbanking',
    icon: Landmark,
    invoice: 'INV-002',
    status: 'completed',
  },
  {
    id: 'PAY-003',
    client: 'StartupX',
    date: '2024-05-27',
    amount: '32,000',
    mode: 'cash',
    icon: IndianRupee,
    invoice: 'INV-003',
    status: 'completed',
  },
  {
    id: 'PAY-004',
    client: 'Global Inc',
    date: '2024-05-26',
    amount: '15,000',
    mode: 'card',
    icon: CreditCard,
    invoice: 'INV-004',
    status: 'pending',
  },
  {
    id: 'PAY-005',
    client: 'Innovation Labs',
    date: '2024-05-25',
    amount: '28,000',
    mode: 'upi',
    icon: Smartphone,
    invoice: 'INV-005',
    status: 'completed',
  },
])

// Filter logic
const filteredPayments = computed(() => {
  return payments.value.filter(payment => {
    const matchesSearch = payment.client.toLowerCase().includes(search.value.toLowerCase())
    const matchesMode = modeFilter.value === 'All Modes' || payment.mode === modeFilter.value.toLowerCase()
    return matchesSearch && matchesMode
  })
})

const modeClasses = {
  upi: 'bg-blue-100 text-blue-600',
  netbanking: 'bg-green-100 text-green-600',
  cash: 'bg-orange-100 text-orange-600',
  card: 'bg-purple-100 text-purple-600',
}

const statusClasses = {
  completed: 'bg-green-100 text-green-700',
  pending: 'bg-orange-100 text-orange-700',
}
</script>
