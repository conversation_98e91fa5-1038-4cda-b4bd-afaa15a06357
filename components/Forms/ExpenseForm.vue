<template>
  <div class="bg-white rounded-lg shadow-sm border p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ isEditing ? 'Edit Expense' : 'Create New Expense' }}
      </h2>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600"
      >
        <Icon name="lucide:x" class="w-6 h-6" />
      </button>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Expense ID
          </label>
          <input
            v-model="form.expenseId"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            placeholder="Auto-generated if empty"
            readonly
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Date *
          </label>
          <input
            v-model="form.date"
            type="date"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Vendor *
          </label>
          <input
            v-model="form.vendor"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Vendor name"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Category *
          </label>
          <select
            v-model="form.category"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select category</option>
            <option
              v-for="category in expenseCategories"
              :key="category"
              :value="category"
            >
              {{ category }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Amount *
          </label>
          <input
            v-model.number="form.amount"
            type="number"
            step="0.01"
            min="0"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0.00"
            @input="calculateTotals"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Tax Rate (%)
          </label>
          <input
            v-model.number="form.taxRate"
            type="number"
            min="0"
            max="100"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="0"
            @input="calculateTotals"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Payment Method
          </label>
          <select
            v-model="form.paymentMethod"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select payment method</option>
            <option value="cash">Cash</option>
            <option value="credit_card">Credit Card</option>
            <option value="debit_card">Debit Card</option>
            <option value="bank_transfer">Bank Transfer</option>
            <option value="check">Check</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Receipt Number
          </label>
          <input
            v-model="form.receiptNumber"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Receipt/Invoice number"
          />
        </div>
      </div>

      <!-- Description -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          v-model="form.description"
          rows="3"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          placeholder="Describe the expense..."
        ></textarea>
      </div>

      <!-- Notes -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Notes
        </label>
        <textarea
          v-model="form.notes"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          placeholder="Additional notes..."
        ></textarea>
      </div>

      <!-- Totals -->
      <div class="bg-gray-50 p-4 rounded-md">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span class="text-sm text-gray-600">Amount:</span>
            <div class="font-medium">${{ form.amount.toFixed(2) }}</div>
          </div>
          <div>
            <span class="text-sm text-gray-600">Tax:</span>
            <div class="font-medium">${{ form.taxAmount.toFixed(2) }}</div>
          </div>
          <div>
            <span class="text-sm text-gray-600">Total:</span>
            <div class="font-bold text-lg">${{ form.totalAmount.toFixed(2) }}</div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          @click="$emit('close')"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="isSubmitting"
          class="px-6 py-2 bg-[#00C951] text-white rounded-md hover:bg-[#00B847] transition-colors disabled:opacity-50"
        >
          {{ isSubmitting ? 'Saving...' : (isEditing ? 'Update Expense' : 'Create Expense') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useExpensesStore } from '~/stores/expenses'
import type { Expense } from '~/services/expenseApi'

interface Props {
  expense?: Expense | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  success: [expense: Expense]
}>()

const expensesStore = useExpensesStore()

const isSubmitting = ref(false)
const isEditing = computed(() => !!props.expense)

// Expense categories
const expenseCategories = [
  'Office Supplies',
  'Travel',
  'Equipment',
  'Utilities',
  'Marketing',
  'Software',
  'Maintenance',
  'Professional Services',
  'Insurance',
  'Rent',
  'Other'
]

// Form data
const form = reactive({
  expenseId: '',
  date: new Date().toISOString().split('T')[0],
  vendor: '',
  category: '',
  amount: 0,
  taxRate: 0,
  taxAmount: 0,
  totalAmount: 0,
  paymentMethod: '',
  receiptNumber: '',
  description: '',
  notes: ''
})

// Load data on mount
onMounted(async () => {
  try {
    // If editing, populate form with expense data
    if (props.expense) {
      populateForm(props.expense)
    }
  } catch (error) {
    console.error('Failed to load expense data:', error)
  }
})

// Populate form with expense data for editing
const populateForm = (expense: Expense) => {
  form.expenseId = expense.expenseId
  form.date = expense.date.split('T')[0]
  form.vendor = expense.vendor
  form.category = expense.category
  form.amount = expense.amount
  form.taxRate = expense.taxRate
  form.taxAmount = expense.taxAmount
  form.totalAmount = expense.totalAmount
  form.paymentMethod = expense.paymentMethod || ''
  form.receiptNumber = expense.receiptNumber || ''
  form.description = expense.description
  form.notes = expense.notes || ''
}

// Calculate totals
const calculateTotals = () => {
  form.taxAmount = (form.amount * form.taxRate) / 100
  form.totalAmount = form.amount + form.taxAmount
}

// Handle form submission
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    const expenseData = {
      ...form,
      status: 'pending' as const
    }
    
    let result
    if (isEditing.value && props.expense) {
      result = await expensesStore.updateExpense(props.expense._id, expenseData)
    } else {
      result = await expensesStore.createExpense(expenseData)
    }
    
    emit('success', result)
    emit('close')
  } catch (error) {
    console.error('Failed to save expense:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>
