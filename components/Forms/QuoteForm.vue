<template>
  <div class="bg-white rounded-lg shadow-sm border p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ isEditing ? 'Edit Quote' : 'Create New Quote' }}
      </h2>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600"
      >
        <Icon name="lucide:x" class="w-6 h-6" />
      </button>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Quote Basic Info -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Quote Number
          </label>
          <input
            v-model="form.quoteNumber"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Auto-generated if empty"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Client *
          </label>
          <select
            v-model="form.clientId"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          >
            <option value="">Select a client</option>
            <option
              v-for="client in clientsStore.clients"
              :key="client._id"
              :value="client._id"
            >
              {{ client.name }} ({{ client.email }})
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Issue Date *
          </label>
          <input
            v-model="form.issueDate"
            type="date"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Valid Until *
          </label>
          <input
            v-model="form.validUntil"
            type="date"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
          />
        </div>
      </div>

      <!-- Quote Items -->
      <div>
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">Quote Items</h3>
          <button
            type="button"
            @click="addItem"
            class="bg-[#00C951] text-white px-4 py-2 rounded-md hover:bg-[#00B847] transition-colors"
          >
            Add Item
          </button>
        </div>

        <div class="space-y-4">
          <div
            v-for="(item, index) in form.items"
            :key="index"
            class="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 border border-gray-200 rounded-md"
          >
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <input
                v-model="item.description"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
                placeholder="Item description"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Quantity *
              </label>
              <input
                v-model.number="item.quantity"
                type="number"
                min="1"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
                @input="calculateItemTotal(index)"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Unit Price *
              </label>
              <input
                v-model.number="item.unitPrice"
                type="number"
                step="0.01"
                min="0"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
                @input="calculateItemTotal(index)"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Total
              </label>
              <input
                :value="item.total"
                type="number"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
              />
            </div>

            <div class="flex items-end">
              <button
                type="button"
                @click="removeItem(index)"
                class="text-red-600 hover:text-red-800 p-2"
              >
                <Icon name="lucide:trash-2" class="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Quote Totals -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Discount (%)
          </label>
          <input
            v-model.number="form.discount"
            type="number"
            min="0"
            max="100"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            @input="calculateTotals"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Tax Rate (%)
          </label>
          <input
            v-model.number="form.taxRate"
            type="number"
            min="0"
            max="100"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            @input="calculateTotals"
          />
        </div>

        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Subtotal:</span>
            <span class="font-medium">${{ form.subtotal.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Discount:</span>
            <span class="font-medium">-${{ form.discountAmount.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-600">Tax:</span>
            <span class="font-medium">${{ form.taxAmount.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between border-t pt-2">
            <span class="font-semibold">Total:</span>
            <span class="font-bold text-lg">${{ form.total.toFixed(2) }}</span>
          </div>
        </div>
      </div>

      <!-- Notes and Terms -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Notes
          </label>
          <textarea
            v-model="form.notes"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Additional notes..."
          ></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Terms & Conditions
          </label>
          <textarea
            v-model="form.terms"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#00C951] focus:border-transparent"
            placeholder="Terms and conditions..."
          ></textarea>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          @click="$emit('close')"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="isSubmitting"
          class="px-6 py-2 bg-[#00C951] text-white rounded-md hover:bg-[#00B847] transition-colors disabled:opacity-50"
        >
          {{ isSubmitting ? 'Saving...' : (isEditing ? 'Update Quote' : 'Create Quote') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useQuotesStore } from '~/stores/quotes'
import { useClientsStore } from '~/stores/clients'
import type { Quote } from '~/services/quoteApi'

interface Props {
  quote?: Quote | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  success: [quote: Quote]
}>()

const quotesStore = useQuotesStore()
const clientsStore = useClientsStore()

const isSubmitting = ref(false)
const isEditing = computed(() => !!props.quote)

// Form data
const form = reactive({
  quoteNumber: '',
  clientId: '',
  issueDate: new Date().toISOString().split('T')[0],
  validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  items: [
    {
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
  ],
  discount: 0,
  taxRate: 0,
  subtotal: 0,
  discountAmount: 0,
  taxAmount: 0,
  total: 0,
  notes: '',
  terms: ''
})

// Load clients on mount
onMounted(async () => {
  try {
    await clientsStore.fetchClients()
    
    // If editing, populate form with quote data
    if (props.quote) {
      populateForm(props.quote)
    }
  } catch (error) {
    console.error('Failed to load clients:', error)
  }
})

// Populate form with quote data for editing
const populateForm = (quote: Quote) => {
  form.quoteNumber = quote.quoteNumber
  form.clientId = quote.clientId
  form.issueDate = quote.issueDate.split('T')[0]
  form.validUntil = quote.validUntil.split('T')[0]
  form.items = [...quote.items]
  form.discount = quote.discount
  form.taxRate = quote.taxRate
  form.notes = quote.notes || ''
  form.terms = quote.terms || ''
  calculateTotals()
}

// Add new item
const addItem = () => {
  form.items.push({
    description: '',
    quantity: 1,
    unitPrice: 0,
    total: 0
  })
}

// Remove item
const removeItem = (index: number) => {
  if (form.items.length > 1) {
    form.items.splice(index, 1)
    calculateTotals()
  }
}

// Calculate item total
const calculateItemTotal = (index: number) => {
  const item = form.items[index]
  item.total = item.quantity * item.unitPrice
  calculateTotals()
}

// Calculate quote totals
const calculateTotals = () => {
  // Calculate subtotal
  form.subtotal = form.items.reduce((sum, item) => sum + item.total, 0)
  
  // Calculate discount amount
  form.discountAmount = (form.subtotal * form.discount) / 100
  
  // Calculate tax amount (after discount)
  const taxableAmount = form.subtotal - form.discountAmount
  form.taxAmount = (taxableAmount * form.taxRate) / 100
  
  // Calculate total
  form.total = form.subtotal - form.discountAmount + form.taxAmount
}

// Handle form submission
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    
    const quoteData = {
      ...form,
      status: 'draft' as const
    }
    
    let result
    if (isEditing.value && props.quote) {
      result = await quotesStore.updateQuote(props.quote._id, quoteData)
    } else {
      result = await quotesStore.createQuote(quoteData)
    }
    
    emit('success', result)
    emit('close')
  } catch (error) {
    console.error('Failed to save quote:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Watch for changes in items to recalculate totals
watch(() => form.items, calculateTotals, { deep: true })
</script>
