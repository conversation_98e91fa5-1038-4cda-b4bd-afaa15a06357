<template>
  <Dialog :open="store.isOpen" @update:open="store.onClose">
    <DialogContent class="z-150">
      <DialogHeader>
        <DialogTitle class="flex justify-center items-center flex-col gap-y-4 pb-2">
          <div class="flex items-center gap-x-2 font-bold text-xl">
            Upgrade to InvoiceGenx
            <Badge variant="premium" class="uppercase text-sm py-1">
              Pro
            </Badge>
          </div>
        </DialogTitle>

        <div class="flex justify-center gap-2 py-2">
          <Button
            :variant="store.billingCycle === 'monthly' ? 'default' : 'outline'"
            @click="store.setBillingCycle('monthly')"
            size="sm"
          >
            Monthly
          </Button>
          <Button
            :variant="store.billingCycle === 'yearly' ? 'default' : 'outline'"
            @click="store.setBillingCycle('yearly')"
            size="sm"
          >
            Yearly
          </Button>
        </div>

        <p class="text-center text-sm text-zinc-500 font-medium">
          {{ billingInfo.label }} – ₹{{ billingInfo.price }}/mo
          <span class="block text-xs">{{ billingInfo.billingNote }}</span>
        </p>

        <DialogDescription class="pt-4 text-zinc-900 font-medium">
  <div class="flex flex-wrap gap-3">
    <div
      v-for="item in premiumModal"
      :key="item.title"
      class="p-2 border-muted border rounded-md flex items-center justify-between hover:shadow-sm transition text-sm w-full sm:w-[48%] lg:w-[31%]"
    >
      <div class="flex items-center gap-x-3">
        <div :class="`p-1.5 rounded-md ${item.bgColor}`">
          <Icon :name="item.icon" :class="`h-6 w-6 ${item.color}`" />
        </div>
        <span class="font-semibold">{{ item.title }}</span>
      </div>
      <Icon name="lucide:check" class="h-4 w-4 text-green-500" />
    </div>
  </div>
</DialogDescription>


      </DialogHeader>

      <DialogFooter class="pt-4">
        <Button variant="premium" size="lg" class="w-full">
          Upgrade
          <Icon name="lucide:zap" class="w-4 h-4 ml-2 fill-white" />
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script lang="ts" setup>
import { useProModal } from '~/stores/useProModal'
import { premiumModal, pricingPlans } from '~/utils'
import { computed } from 'vue'

const store = useProModal()

const billingInfo = computed(() => pricingPlans[store.billingCycle])
</script>
