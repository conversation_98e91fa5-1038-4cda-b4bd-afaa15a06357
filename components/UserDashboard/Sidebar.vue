<template>
  <div class="px-4 h-full bg-white shadow-sm border-r flex flex-col p-2">
    <!-- Sticky Profile Header -->
    <div class="sticky top-0 z-10 bg-white py-4">
      <div class="flex items-center justify-between px-2">
        <NuxtLink to="/" class="flex items-center space-x-2">
          <img src="/assets/invoice-easy-logo.png" alt="Company Logo" class="h-12 w-12 rounded-xl" />
          <h1 class="text-lg font-bold">Invoice @Easy</h1>
        </NuxtLink>
        <button @click="toggleNav" class="text-sm px-2 py-2 rounded-lg cursor-pointer">
          <Icon :name="navOpen ? 'lucide:chevron-down' : 'lucide:chevron-up'" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Sidebar Links -->
    <div class="overflow-y-auto hide-scrollbar flex-1 mt-4" v-show="navOpen">
      <div class="space-y-1 px-2">
        <template v-for="route in dashboardLinks" :key="route.label + route.link">
          <!-- With children -->
          <div v-if="route.children">
            <div
              @click="toggleChild(route.label)"
              class="text-sm group text-primary flex p-3 w-full justify-between font-medium cursor-pointer rounded-lg hover:bg-green-400 hover:text-white"
              :class="{ 'bg-green-400 text-white': currentRoute.path.startsWith(route.link) }"
            >
              <div class="flex items-center">
                <Icon
                  :name="route.icon"
                  :class="[ 'h-5 w-5 mr-3', { [route.color]: !currentRoute.path.startsWith(route.link), 'text-white': currentRoute.path.startsWith(route.link) }, 'group-hover:text-white' ]"
                />
                {{ route.label }}
              </div>
              <Icon
                :name="openedChildren[route.label] ? 'lucide:chevron-down' : 'lucide:chevron-right'"
                :class="[ 'h-4 w-4', { 'text-white': currentRoute.path.startsWith(route.link) }, 'group-hover:text-white' ]"
              />
            </div>

            <!-- Child Links -->
            <div v-if="openedChildren[route.label]" class="ml-6 mt-1 space-y-1 transition-all duration-300">
              <NuxtLink
                v-for="child in route.children"
                :key="child.link"
                :to="child.link"
                @click="$emit('navigate')"
                class="text-sm group text-primary flex items-center p-3 w-full justify-start font-medium cursor-pointer rounded-lg hover:bg-green-400 hover:text-white"
                :class="{ 'bg-green-400 text-white': currentRoute.path === child.link }"
              >
                <Icon
                  :name="child.icon"
                  :class="[ 'h-5 w-5 mr-3', { [child.color]: currentRoute.path !== child.link, 'text-white': currentRoute.path === child.link }, 'group-hover:text-white' ]"
                />
                {{ child.label }}
              </NuxtLink>
            </div>
          </div>

          <!-- No children -->
          <NuxtLink
            v-else
            :to="route.link"
            @click="$emit('navigate')"
            class="text-sm group text-primary flex p-3 w-full justify-start font-medium cursor-pointer rounded-lg hover:bg-green-400 hover:text-white"
            :class="{ 'bg-green-400 text-white': currentRoute.path === route.link }"
          >
            <Icon
              :name="route.icon"
              :class="[ 'h-5 w-5 mr-3', { [route.color]: currentRoute.path !== route.link, 'text-white': currentRoute.path === route.link }, 'group-hover:text-white' ]"
            />
            {{ route.label }}
          </NuxtLink>
        </template>
      </div>
    </div>

    <!-- upgrade and counter -->
    <div class="px-3 border-t border-b">
      <div class="bg-white/10 border-0">
        <div class="py-6 px-2">
          <div class="text-center text-sm mb-4 space-y-4">
            <p>{{ used }} / {{ total }}</p>
            <progress :value="used" :max="total" class="w-full" />
          </div>
          <Button @click="store.onOpen" variant="premium" class="w-full">
            Upgrade
            <Icon name="lucide:zap" class="w-4 h-4 ml-2 fill-white" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useProModal } from '~/stores/useProModal'
const store = useProModal();
const currentRoute = useRoute();

const navOpen = ref(true);
const toggleNav = () => (navOpen.value = !navOpen.value);

const openedChildren = reactive<Record<string, boolean>>({});
function toggleChild(label: string) {
  openedChildren[label] = !openedChildren[label];
}

const used = ref(0);
const total = ref(10);

onMounted(() => {
  setTimeout(() => {
    used.value = 4; // example: 4 out of 10 used
  }, 500);
});

defineEmits(['navigate']);
</script>

<style scoped>
.hide-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.hide-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}
</style>

