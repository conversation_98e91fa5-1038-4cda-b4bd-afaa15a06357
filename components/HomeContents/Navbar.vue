<template>
    <!-- Desktop Navbar -->
    <nav class="hidden md:block py-2 font-serif bg-white border-b border-gray-400 sticky top-0 z-50 shadow-md">
      <div class="p-2 flex items-center justify-between">
        <div class="flex items-center gap-1">
          <img class="w-10 h-10 rounded-lg hover:bg-green-300" src="/assets/invoice-easy-logo.png" >
          <a href="#">
            <p class="font-bold text-xl">invoice<span class="text-green-500">@Easy</span></p>
          </a>
        </div>
        <div class="flex sm:gap-3 items-center md:gap-4 xl:gap-8">
          <a href="/Auth/home">Home</a>
          <a href="#">Features</a>
          <a href="#">Pricing</a>
          <a href="#">Contact Us</a>
        </div>
        <div class="flex gap-5">
          <a href="/Auth/register" class="btn-primary">Sign up</a>
          <a href="/Auth/login" class="btn-secondary">Login</a>
        </div>
      </div>
    </nav>
  
    <!-- Mobile Navbar -->
    <div class="md:hidden font-serif bg-white sticky top-0 z-50 shadow-md">
      <div class="flex items-center justify-between px-4 py-4">
        <div class="flex items-center gap-2">
          <img class="w-10 h-10 rounded-lg hover:bg-green-300" src="/assets/invoice-easy-logo.png" >
          <a href="#">
            <p class="font-bold text-xl">invoice<span class="text-green-500">@Easy</span></p>
          </a>
        </div>
        <button class="focus:outline-none" @click.stop="isOpen = !isOpen">
          <svg v-if="!isOpen" xmlns="http://www.w3.org/2000/svg" height="32" viewBox="0 -960 960 960" width="32" fill="#000">
            <path d="M120-240v-60h720v60H120Zm0-210v-60h720v60H120Zm0-210v-60h720v60H120Z"/>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" height="32" viewBox="0 -960 960 960" width="32" fill="#000">
            <path d="m249-207-42-42 231-231-231-231 42-42 231 231 231-231 42 42-231 231 231 231-42 42-231-231-231 231Z"/>
          </svg>
        </button>
      </div>
  
      <!-- Slide-in Mobile Menu -->
      <transition name="slide-left">
        <div
          v-if="isOpen"
          ref="menuRef"
          class="fixed font-serif top-0 left-0 h-full w-64 z-40 p-6 glassmorphism backdrop-blur-md flex flex-col space-y-4"
        >
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-2">
              <img class="w-10 h-10 rounded-lg hover:bg-green-300" src="/assets/invoice-easy-logo.png" >
              <a href="#">
                <p class="font-bold text-xl">invoice<span class="text-green-500">@Easy</span></p>
              </a>
            </div>
            <!-- <button @click.stop="isOpen = false">
              <svg xmlns="http://www.w3.org/2000/svg" height="28" viewBox="0 -960 960 960" width="28" fill="#000">
                <path d="m249-207-42-42 231-231-231-231 42-42 231 231 231-231 42 42-231 231 231 231-42 42-231-231-231 231Z"/>
              </svg>
            </button> -->
          </div>
          <a href="/Auth/home" class="block">Home</a>
          <a href="#" class="block">Features</a>
          <a href="#" class="block">Pricing</a>
          <a href="#" class="block">Contact Us</a>
          <div class="pt-4">
            <a href="/register" class="btn-primary block text-center mb-2">Sign up</a>
            <a href="/login" class="btn-secondary block text-center">Login</a>
          </div>
        </div>
      </transition>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue'
  
  const isOpen = ref(false)
  const menuRef = ref(null)
  
  const handleClickOutside = (event) => {
    if (menuRef.value && !menuRef.value.contains(event.target)) {
      isOpen.value = false
    }
  }
  
  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
  })
  
  onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
  })
  </script>
  
  <style scoped>
  /* Glassmorphism */
  .glassmorphism {
    background: rgba(255, 255, 255, 0.25);
    border-radius: 0.75rem;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  /* Slide-in from left */
  .slide-left-enter-active,
  .slide-left-leave-active {
    transition: all 0.3s ease;
  }
  .slide-left-enter-from {
    transform: translateX(-100%);
    opacity: 0;
  }
  .slide-left-enter-to {
    transform: translateX(0);
    opacity: 1;
  }
  .slide-left-leave-from {
    transform: translateX(0);
    opacity: 1;
  }
  .slide-left-leave-to {
    transform: translateX(-100%);
    opacity: 0;
  }
  </style>
  