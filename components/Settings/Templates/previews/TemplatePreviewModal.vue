<template>
  <Transition name="fade">
    <div
      v-if="show"
      class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-xl max-w-3xl w-full p-6 shadow-lg relative">
        <button
          class="absolute top-2 right-2 text-gray-500 hover:text-black text-2xl"
          @click="$emit('close')"
        >
          ×
        </button>
        <component :is="componentMap[templateKey]" />
      </div>
    </div>
  </Transition>
</template>

<script setup>
defineProps({
  show: Boolean,
  templateKey: String
})
defineEmits(['close'])

const componentMap = {
  'a4-professional': resolveComponent('previews/A4Professional'),
  'thermal-printer': resolveComponent('previews/ThermalPrinter'),
  'mobile-receipt': resolveComponent('previews/MobileReceipt'),
}
</script>
