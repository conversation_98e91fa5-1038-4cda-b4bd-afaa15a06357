<template>
  <section class="p-6 max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6 md:py-5">
    <!-- Business Information Card -->
    <div class="bg-white p-6 rounded-xl shadow-md border">
      <h2 class="text-xl font-semibold mb-6 flex items-center gap-2">
        <Building2 class="w-5 h-5 text-indigo-600" /> Business Information
      </h2>
      <div class="flex items-center gap-4 mb-6">
        <div class="w-16 h-16 rounded-full bg-indigo-500 text-white flex items-center justify-center text-xl font-bold">
          IE
        </div>
        <button class="flex items-center gap-2 px-4 py-2 border rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100">
          <Upload class="w-4 h-4" /> Upload Logo
        </button>
      </div>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Business Name</label>
          <input type="text" placeholder="Invoice@Easy Solutions" class="mt-1 w-full border rounded-md p-2" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Owner Name</label>
          <input type="text" placeholder="John Doe" class="mt-1 w-full border rounded-md p-2" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">GST Number</label>
          <input type="text" placeholder="GST123456789" class="mt-1 w-full border rounded-md p-2" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">PAN Number</label>
          <input type="text" placeholder="**********" class="mt-1 w-full border rounded-md p-2" />
        </div>
      </div>
    </div>

    <!-- Contact Information Card -->
    <div class="bg-white p-6 rounded-xl shadow-md border">
      <h2 class="text-xl font-semibold mb-6 flex items-center gap-2">
        <Mail class="w-5 h-5 text-indigo-600" /> Contact Information
      </h2>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 flex items-center gap-2">
            <Mail class="w-4 h-4" /> Email Address
          </label>
          <input type="email" placeholder="<EMAIL>" class="mt-1 w-full border rounded-md p-2" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 flex items-center gap-2">
            <Phone class="w-4 h-4" /> Phone Number
          </label>
          <input type="tel" placeholder="+91 98765 43210" class="mt-1 w-full border rounded-md p-2" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 flex items-center gap-2">
            <MapPin class="w-4 h-4" /> Business Address
          </label>
          <textarea rows="3" placeholder="123 Business Street, Mumbai, Maharashtra 400001" class="mt-1 w-full border rounded-md p-2"></textarea>
        </div>
        <Button variant="premium" class="w-full">
          <Save class="w-4 h-4" /> Save Changes
        </Button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { Mail, Phone, MapPin, Upload, Save, Building2 } from 'lucide-vue-next'
</script>

<style scoped>
/* Responsive tweaks if needed */
</style>