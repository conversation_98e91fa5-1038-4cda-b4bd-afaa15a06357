<template>
  <div class="md:py-5 p-6">
    <div class="max-w-7xl mx-auto bg-white shadow p-3 rounded-2xl space-y-6 border">
    <!-- Title -->
    <div class="flex items-center space-x-3">
      <ShieldCheck class="text-primary" />
      <h2 class="text-2xl font-semibold">Security Settings</h2>
    </div>

    <div v-for="(item, i) in notifications" :key="i" class="flex items-center justify-between">
      <div>
        <div class="flex items-center gap-2">
          <component :is="item.icon" v-if="item.icon" class="w-4 h-4 text-muted-foreground" />
          <h3 class="font-medium">{{ item.label }}</h3>
          <span v-if="item.pro" variant="premium" class="text-xs bg-gradient-to-r from-green-400 via-green-500 to-green-600 text-white px-2 py-0.5 rounded-md">Pro</span>
        </div>
        <p class="text-sm text-gray-500">{{ item.description }}</p>
      </div>
      <label class="inline-flex items-center me-5 cursor-pointer">
  <input type="checkbox" value="" class="sr-only peer" :checked="item.enabled" @change="toggle(i)">
  <div class="relative w-11 h-6 bg-gray-200 rounded-full peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600 dark:peer-checked:bg-green-600"></div>
  <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300"></span>
</label>
    </div>

    <!-- Session Timeout -->
    <div class="space-y-2 border-b pb-4">
      <label class="font-semibold">Session Timeout (minutes)</label>
      <select
        v-model="sessionTimeout"
        class="w-full border rounded-lg p-2 focus:ring-2 ring-offset-1"
      >
        <option value="15">15 minutes</option>
        <option value="30">30 minutes</option>
        <option value="60">60 minutes</option>
      </select>
    </div>

    <!-- Login Activity -->
    <div class="space-y-3">
      <h3 class="font-semibold">Recent Login Activity</h3>

      <div class="border p-3 rounded-lg flex justify-between items-center">
        <div>
          <p class="font-medium">Current Session</p>
          <p class="text-sm text-gray-500">Chrome on Windows • Mumbai, India</p>
        </div>
        <span class="text-xs bg-green-400 text-white px-2 py-1 rounded-lg">Active</span>
      </div>

      <div class="border p-3 rounded-lg flex justify-between items-center">
        <div>
          <p class="font-medium">Mobile App</p>
          <p class="text-sm text-gray-500">Android • 2 hours ago</p>
        </div>
        <Eye class="text-gray-500 w-5 h-5" />
      </div>
    </div>

    <!-- Save Button -->
    <div>
      <Button variant="premium"
        class="w-full text-white py-3 rounded-xl font-semibold flex items-center justify-center gap-2"
      >
        <Save class="w-5 h-5" />
        Save Security Settings
      </Button>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ShieldCheck, Eye, Save } from 'lucide-vue-next'

const notifications = ref([
  {
    label: 'Two-Factor Authentication',
    description: 'Add an extra layer of security to your account',
    enabled: true
  },
  {
    label: 'Login Notifications',
    description: 'Get notified of new device logins',
    enabled: false,
    icon: 'LucideSmartphone',
    pro: true
  },
])

const toggle = (index) => {
  notifications.value[index].enabled = !notifications.value[index].enabled
}
const sessionTimeout = ref('30')
</script>
