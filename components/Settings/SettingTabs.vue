<template>
  <div class="w-full bg-muted/60 p-2 rounded-xl flex items-center justify-between">
    <button
      v-for="tab in tabs"
      :key="tab.name"
      @click="onTabClick(tab.name)"
      :class="[
        'flex items-center gap-2 px-6 py-2 rounded-lg text-sm font-medium transition-all',
        selected === tab.name ? 'bg-white shadow-sm text-black' : 'text-muted-foreground hover:bg-muted/80'
      ]"
    >
      <component :is="tab.icon" class="w-4 h-4" />
      {{ tab.name }}
    </button>
  </div>
</template>

<script setup>
import { User, Bell, Shield, FileText } from 'lucide-vue-next'

const props = defineProps({
  selected: String,
  tabs: Array
})

const emit = defineEmits(['select'])

function onTabClick(name) {
  emit('select', name)
}
</script>
