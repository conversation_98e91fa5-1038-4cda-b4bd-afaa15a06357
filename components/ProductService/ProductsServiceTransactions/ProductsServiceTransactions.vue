<template>
  <Tabs default-value="products" class="">
    <div class="pl-6">
      <TabsList class="grid grid-cols-2 w-60 mt-6">
      <TabsTrigger value="products">Products</TabsTrigger>
      <TabsTrigger value="services">Services</TabsTrigger>
    </TabsList>
    </div>

    <TabsContent value="products">
      <RecentProducts :items="products" type="product" />
    </TabsContent>

    <TabsContent value="services">
      <RecentProducts :items="services" type="service" />
    </TabsContent>
  </Tabs>
</template>

<script setup lang="ts">
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

defineProps<{
  products: any[]
  services: any[]
}>()
</script>
