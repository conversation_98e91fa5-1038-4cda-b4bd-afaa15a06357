<template>
  <div class="p-4 sm:p-6">
    <!-- Search + Filter -->
    <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
     <!-- search -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
          </div>
        </div>

      <!-- Filter Dropdown -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline">
            <Filter class="w-4 h-4 mr-2" />
            Filter
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="sortBy('recent')">Recent</DropdownMenuItem>
          <DropdownMenuItem @click="sortBy('oldest')">Oldest</DropdownMenuItem>
          <DropdownMenuItem @click="sortBy('high')">Highest Price</DropdownMenuItem>
          <DropdownMenuItem @click="sortBy('low')">Lowest Price</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto rounded-xl border">
      <table class="min-w-full text-sm text-left">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3">ID</th>
            <th class="px-4 py-3">Name</th>
            <th class="px-4 py-3">Unit</th>
            <th v-if="type === 'service'" class="px-4 py-3">Qty</th>
            <th class="px-4 py-3">Discount</th>
            <th class="px-4 py-3">Price</th>
            <th class="px-4 py-3">Tax</th>
            <th class="px-4 py-3">Description</th>
            <th class="px-4 py-3 text-right">Actions</th>
          </tr>
        </thead>
        <tbody>
          <!-- Loading State -->
          <tr v-if="isLoading">
            <td :colspan="type === 'service' ? 9 : 8" class="px-4 py-8 text-center">
              <div class="flex justify-center items-center">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00C951]"></div>
                <span class="ml-2 text-gray-600">Loading {{ type }}s...</span>
              </div>
            </td>
          </tr>

          <!-- Error State -->
          <tr v-else-if="hasError">
            <td :colspan="type === 'service' ? 9 : 8" class="px-4 py-8 text-center">
              <div class="text-red-600">Failed to load {{ type }}s</div>
              <button
                @click="refreshData"
                class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
              >
                Try again
              </button>
            </td>
          </tr>

          <!-- No Data State -->
          <tr v-else-if="paginatedItems.length === 0">
            <td :colspan="type === 'service' ? 9 : 8" class="px-4 py-8 text-center text-gray-500">
              No {{ type }}s found
            </td>
          </tr>

          <!-- Data Rows -->
          <tr
            v-else
            v-for="item in paginatedItems"
            :key="getItemId(item)"
            class="border-t hover:bg-gray-50"
          >
            <td class="px-4 py-3 font-semibold">{{ getItemId(item) }}</td>
            <td class="px-4 py-3">{{ item.name }}</td>
            <td class="px-4 py-3">{{ getItemUnit(item) }}</td>
            <td v-if="type === 'service'" class="px-4 py-3">{{ getServiceQuantity(item) }}</td>
            <td class="px-4 py-3">{{ getItemDiscount(item) }}%</td>
            <td class="px-4 py-3">${{ getItemPrice(item) }}</td>
            <td class="px-4 py-3">{{ getItemTax(item) }}%</td>
            <td class="px-4 py-3">{{ item.description || 'N/A' }}</td>
            <td class="px-4 py-3 text-right">
              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="ghost" size="icon">
                    <MoreVertical class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem @click="viewItem(item)">View</DropdownMenuItem>
                  <DropdownMenuItem @click="editItem(item)">Edit</DropdownMenuItem>
                  <DropdownMenuItem class="text-red-600" @click="deleteItem(item)">Delete</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="mt-6 flex justify-center gap-2 items-center text-sm">
      <button
        class="px-3 py-1 border rounded hover:bg-gray-100"
        :disabled="currentPage === 1"
        @click="currentPage--"
      >
        Prev
      </button>
      <span>Page {{ currentPage }} of {{ totalPages }}</span>
      <button
        class="px-3 py-1 border rounded hover:bg-gray-100"
        :disabled="currentPage === totalPages"
        @click="currentPage++"
      >
        Next
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Filter, MoreVertical } from 'lucide-vue-next'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useProductsStore } from '~/stores/products'
import { useServicesStore } from '~/stores/services'

const props = defineProps<{
  items: any[]
  type: 'product' | 'service'
}>()

const productsStore = useProductsStore()
const servicesStore = useServicesStore()

const searchQuery = ref('')
const sortType = ref('recent')

const currentPage = ref(1)
const itemsPerPage = 5

// Computed properties for loading and error states
const isLoading = computed(() => {
  return props.type === 'product' ? productsStore.isLoading : servicesStore.isLoading
})

const hasError = computed(() => {
  return props.type === 'product' ? productsStore.hasError : servicesStore.hasError
})

// Get actual data from stores instead of props
const storeItems = computed(() => {
  return props.type === 'product' ? productsStore.products : servicesStore.services
})

// Fetch data on component mount
onMounted(async () => {
  try {
    if (props.type === 'product') {
      await productsStore.fetchProducts({ limit: 20 })
    } else {
      await servicesStore.fetchServices({ limit: 20 })
    }
  } catch (error) {
    console.error(`Failed to load ${props.type}s:`, error)
  }
})

// Refresh data
const refreshData = async () => {
  try {
    if (props.type === 'product') {
      await productsStore.fetchProducts({ limit: 20 })
    } else {
      await servicesStore.fetchServices({ limit: 20 })
    }
  } catch (error) {
    console.error(`Failed to refresh ${props.type}s:`, error)
  }
}

const filteredItems = computed(() => {
  const query = searchQuery.value.toLowerCase()

  return storeItems.value.filter((item: any) => {
    const price = getItemPrice(item)
    const id = getItemId(item)
    return (
      id.toLowerCase().includes(query) ||
      item.name.toLowerCase().includes(query) ||
      getItemUnit(item).toLowerCase().includes(query) ||
      (item.description || '').toLowerCase().includes(query) ||
      price.toString().includes(query)
    )
  }).sort((a: any, b: any) => {
    const priceA = getItemPrice(a)
    const priceB = getItemPrice(b)
    if (sortType.value === 'high') return priceB - priceA
    if (sortType.value === 'low') return priceA - priceB
    if (sortType.value === 'oldest') return 1 // reverse order
    return -1 // recent
  })
})

// Helper functions to map different data structures
const getItemId = (item: any) => {
  return props.type === 'product' ? item.productId : item.serviceId
}

const getItemUnit = (item: any) => {
  return props.type === 'product' ? item.unit : 'service'
}

const getItemPrice = (item: any) => {
  return props.type === 'product' ? item.sellingPrice : item.price
}

const getItemTax = (item: any) => {
  return item.taxRate || 0
}

const getItemDiscount = (item: any) => {
  return 0 // No discount field in current models, can be added later
}

const getServiceQuantity = (item: any) => {
  return props.type === 'service' ? 1 : 0 // Services typically have quantity 1
}

// Action handlers
const viewItem = (item: any) => {
  console.log('View item:', item)
  // TODO: Navigate to item detail page
}

const editItem = (item: any) => {
  console.log('Edit item:', item)
  // TODO: Navigate to item edit page
}

const deleteItem = async (item: any) => {
  const itemType = props.type

  if (confirm(`Are you sure you want to delete this ${itemType}?`)) {
    try {
      if (itemType === 'product') {
        await productsStore.deleteProduct(item._id)
      } else {
        await servicesStore.deleteService(item._id)
      }
      console.log(`${itemType} deleted successfully`)
    } catch (error) {
      console.error(`Failed to delete ${itemType}:`, error)
    }
  }
}

const totalPages = computed(() =>
  Math.ceil(filteredItems.value.length / itemsPerPage)
)

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  return filteredItems.value.slice(start, start + itemsPerPage)
})

watch(filteredItems, () => {
  currentPage.value = 1 // reset to first page if filter changes
})

function sortBy(type: string) {
  sortType.value = type
}
</script>
