<template>
  <Dialog :open="open" @update:open="emit('close')">
    <DialogContent>
      <DialogHeader>
        <DialogTitle class="text-black">Add Product</DialogTitle>
        <DialogDescription class="text-gray-600">
          Fill in product details below.
        </DialogDescription>
      </DialogHeader>

      <!-- Scrollable Form -->
      <div class="h-[400px] md:max-h-[70vh] overflow-y-auto pr-2 mt-4 custom-scroll">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium">Product ID (auto-generated)</label>
            <Input v-model="product.productId" readonly class="bg-gray-100 cursor-not-allowed" />
          </div>
          <div>
            <label class="text-sm font-medium">
              Product Name <span class="text-red-500">*</span>
            </label>
            <Input v-model="product.name" :class="{'border-red-500': errors.name}" />
          </div>
          <div>
            <label class="text-sm font-medium">
              Unit <span class="text-red-500">*</span>
            </label>
            <select v-model="product.unit" class="border rounded px-2 py-1 w-full" :class="{'border-red-500': errors.unit}">
              <option disabled value="">Select unit</option>
              <option value="kg">kg</option>
              <option value="gram">gram</option>
              <option value="piece">piece</option>
              <option value="box">box</option>
            </select>
          </div>
          <div>
            <label class="text-sm font-medium">
              Stock <span class="text-red-500">*</span>
            </label>
            <Input v-model.number="product.stock" type="number" min="0" :class="{'border-red-500': errors.stock}" />
          </div>
          <div>
            <label class="text-sm font-medium">Discount (%)</label>
            <Input v-model.number="product.discount" type="number" min="0" max="100" />
          </div>
          <div>
            <label class="text-sm font-medium">
              Selling Price <span class="text-red-500">*</span>
            </label>
            <Input v-model.number="product.sellingPrice" type="number" min="0" :class="{'border-red-500': errors.price}" />
          </div>
          <div class="md:col-span-2">
            <label class="text-sm font-medium">Tax Rate (%)</label>
            <Input v-model.number="product.taxRate" type="number" min="0" />
          </div>
          <div class="md:col-span-2">
            <label class="text-sm font-medium">Description</label>
            <Textarea v-model="product.description" rows="3" />
          </div>
        </div>
      </div>

      <!-- Footer -->
      <DialogFooter class="mt-6 flex justify-end gap-2">
        <Button variant="outline" @click="emit('close')">Cancel</Button>
        <Button
          class="bg-green-400 text-white"
          :disabled="!isFormValid"
          @click="submitProduct"
        >
          Add Product
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { ref, defineEmits, defineProps, watch } from 'vue'

const emit = defineEmits(['close', 'add-product'])
const props = defineProps<{ open: boolean }>()

// Start product ID number
let lastProductNumber = 100

function generateProductId() {
  lastProductNumber++
  return `p-${lastProductNumber}`
}

// Initialize product with proper field names matching backend model
const product = ref({
  productId: generateProductId(),
  name: '',
  unit: '',
  stock: 0,
  discount: 0,
  sellingPrice: 0,
  taxRate: 0,
  description: ''
})

const errors = ref({
  name: false,
  unit: false,
  stock: false,
  price: false
})

function resetForm() {
  product.value.name = ''
  product.value.unit = ''
  product.value.stock = 0
  product.value.discount = 0
  product.value.sellingPrice = 0
  product.value.taxRate = 0
  product.value.description = ''
  errors.value = {
    name: false,
    unit: false,
    stock: false,
    price: false
  }
}

// Reset form fields (except productId) when dialog opens
watch(() => props.open, (val) => {
  if (val) {
    resetForm()
  }
})

function validateForm() {
  errors.value.name = !product.value.name.trim()
  errors.value.unit = !product.value.unit
  errors.value.stock = product.value.stock === null || product.value.stock < 0
  errors.value.price = product.value.sellingPrice === null || product.value.sellingPrice <= 0
  return !(errors.value.name || errors.value.unit || errors.value.stock || errors.value.price)
}

const isFormValid = ref(false)
watch(product, () => {
  isFormValid.value = validateForm()
}, { deep: true })

function submitProduct() {
  if (!validateForm()) return

  emit('add-product', { ...product.value })

  // Generate new product ID for next product after submission
  product.value.productId = generateProductId()

  // Reset all fields except product ID for new entry
  resetForm()

  emit('close')
}
</script>

<style scoped>
.custom-scroll::-webkit-scrollbar {
  display: none;
}
.custom-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.border-red-500 {
  border-color: #f87171 !important;
}
</style>
