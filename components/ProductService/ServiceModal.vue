<template>
  <div>
    <Dialog :open="open" @update:open="emit('close')">
      <DialogContent>
        <DialogHeader>
          <DialogTitle class="text-black">Add Service</DialogTitle>
          <DialogDescription class="text-gray-600">Fill in service details below.</DialogDescription>
        </DialogHeader>

        <div class="h-[400px] md:max-h-[70vh] overflow-y-auto pr-2 mt-4 custom-scroll">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block mb-1 text-sm font-medium text-black">Service ID</label>
              <Input v-model="service.serviceId" class="w-full bg-gray-100" readonly />
            </div>

            <div>
              <label class="block mb-1 text-sm font-medium text-black">
                Service Name <span class="text-red-500">*</span>
              </label>
              <Input v-model="service.name" class="w-full" />
            </div>

            <div>
              <label class="block mb-1 text-sm font-medium text-black">
                Category <span class="text-red-500">*</span>
              </label>
              <select v-model="service.category" class="w-full border rounded px-3 py-2">
                <option disabled value="">Select Category</option>
                <option value="consulting">Consulting</option>
                <option value="design">Design</option>
                <option value="development">Development</option>
                <option value="marketing">Marketing</option>
                <option value="maintenance">Maintenance</option>
                <option value="support">Support</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label class="block mb-1 text-sm font-medium text-black">
                Price <span class="text-red-500">*</span>
              </label>
              <Input v-model.number="service.price" type="number" min="0" step="0.01" class="w-full" />
            </div>

            <div class="md:col-span-2">
              <label class="block mb-1 text-sm font-medium text-black">Tax Rate (%)</label>
              <Input v-model.number="service.taxRate" type="number" min="0" max="100" class="w-full" />
            </div>

            <div class="md:col-span-2">
              <label class="block mb-1 text-sm font-medium text-black">Description</label>
              <Textarea v-model="service.description" class="w-full" rows="4" />
            </div>
          </div>
        </div>

        <DialogFooter class="mt-6 flex justify-end">
          <Button variant="outline" class="mr-2 border-black text-black" @click="emit('close')">Cancel</Button>
          <Button class="bg-green-400 hover:bg-green-500 text-white" @click="submitService">Add Service</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import {
  Dialog, DialogContent, DialogDescription,
  DialogFooter, DialogHeader, DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { ref, defineEmits, defineProps, onMounted } from 'vue'

const emit = defineEmits(['close', 'add-service'])
const props = defineProps<{ open: boolean }>()

const nextId = ref(201)

const service = ref({
  serviceId: '',
  name: '',
  category: '',
  price: 0,
  taxRate: 0,
  description: '',
})

function generateServiceId() {
  service.value.serviceId = `S-${nextId.value++}`
}

function submitService() {
  const { serviceId, name, category, price } = service.value
  if (!serviceId || !name || !category || price <= 0) {
    alert('Please fill all required fields with valid data.')
    return
  }

  emit('add-service', { ...service.value })

  // Reset with new service ID
  generateServiceId()
  service.value = {
    ...service.value,
    name: '',
    category: '',
    price: 0,
    taxRate: 0,
    description: '',
  }
}

onMounted(() => {
  generateServiceId()
})
</script>

<style scoped>
.custom-scroll::-webkit-scrollbar {
  display: none;
}
.custom-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
