<template>
    <Heading
      title="Manage Products"
      description="Manage your Products and their details"
      icon="lucide:users"
      iconColor="text-green-400"
      bgColor="bg-white"
      :buttons="[
        {
          label: 'Back to Home',
          icon: 'lucide:arrow-left',
          bgColor: 'bg-[#00C951]',
          textColor: 'text-white',
          to: '/products-service'
        },
      ]"
    />
  <div class="p-6 space-y-6">
    <!-- Header -->
    <h1 class="text-2xl font-bold">Product & Service Catalog ({{ filteredItems.length }})</h1>

    <!-- Filters -->
    <div class="flex flex-col md:flex-row justify-between gap-4">
      <div class="flex-1">
        <div class="relative">
            <div
          class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
          style="--tw-ring-color: #05DF72"
        >
          <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
          <input
            v-model="search"
            type="text"
            placeholder="Search All..."
            class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
          />
        </div>
          <!-- <input
            v-model="search"
            type="text"
            placeholder="Search products..."
            class="w-full pl-10 pr-4 py-2 border rounded-md"
          />
          <Icon name="lucide:search" class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" /> -->
        </div>
      </div>

      <div class="flex gap-4">
        <select v-model="selectedType" class="px-4 py-2 border rounded-md">
          <option value="">All Types</option>
          <option value="Product">Product</option>
          <option value="Service">Service</option>
        </select>
        <select v-model="selectedCategory" class="px-4 py-2 border rounded-md">
          <option value="">All</option>
          <option value="Digital Products">Digital Products</option>
          <option value="Development">Development</option>
          <option value="Design">Design</option>
        </select>
      </div>
    </div>

    <!-- Product Cards -->
    <div class="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div
        v-for="item in filteredItems"
        :key="item.code"
        class="bg-white rounded-xl border shadow p-5 relative"
      >
        <!-- Top Icons -->
        <div class="absolute top-3 right-3 flex gap-2">
          <Icon name="lucide:edit-3" class="w-4 h-4 text-gray-600 cursor-pointer" />
          <Icon name="lucide:trash" class="w-4 h-4 text-red-500 cursor-pointer" />
        </div>

        <!-- Icon and Header -->
        <div class="flex items-start gap-3">
          <div class="bg-blue-100 p-2 rounded-lg">
            <Icon :name="item.icon" class="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 class="font-semibold text-gray-900">{{ item.name }}</h2>
            <p class="text-sm text-blue-600 font-medium">{{ item.code }}</p>
            <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full mt-1 inline-block">{{ item.category }}</span>
          </div>
        </div>

        <!-- Description -->
        <p class="text-sm text-gray-600 mt-3">{{ item.description }}</p>

        <!-- Info -->
        <div class="mt-4 space-y-1 text-sm text-gray-700">
          <div><span class="text-gray-500">Unit:</span> <strong class="float-right">{{ item.unit }}</strong></div>
          <div v-if="item.stock !== null"><span class="text-gray-500">Stock:</span> <strong class="float-right">{{ item.stock }}</strong></div>
          <div><span class="text-gray-500">Discount:</span> <strong class="float-right">{{ item.discount }}%</strong></div>
          <div><span class="text-gray-500">Tax:</span> <strong class="float-right">{{ item.tax }}%</strong></div>
        </div>

        <hr class="my-4" />

        <!-- Price -->
        <div class="flex justify-between items-center">
          <div class="text-green-600 text-xl font-semibold">${{ item.price }}</div>
          <span
            :class="[
              'text-xs px-2 py-1 rounded-full font-medium',
              item.type === 'Product' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
            ]"
          >
            {{ item.type }}
          </span>
        </div>

        <p class="text-sm text-gray-500">per {{ item.unit }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const search = ref('')
const selectedType = ref('')
const selectedCategory = ref('')

const items = ref([
  {
    name: 'Premium WordPress Theme',
    code: 'P-101',
    description: 'Professional WordPress theme with advanced features',
    unit: 'license',
    stock: 50,
    discount: 10,
    tax: 8,
    price: 89,
    category: 'Digital Products',
    type: 'Product',
    icon: 'lucide:package'
  },
  {
    name: 'Mobile App Template',
    code: 'P-102',
    description: 'React Native template for e-commerce apps',
    unit: 'license',
    stock: 25,
    discount: 0,
    tax: 8,
    price: 149,
    category: 'Digital Products',
    type: 'Product',
    icon: 'lucide:package'
  },
  {
    name: 'Web Development',
    code: 'S-201',
    description: 'Custom website development services',
    unit: 'hour',
    stock: null,
    discount: 0,
    tax: 10,
    price: 150,
    category: 'Development',
    type: 'Service',
    icon: 'lucide:clock'
  },
  {
    name: 'Logo Design',
    code: 'S-202',
    description: 'Professional logo design package',
    unit: 'hour',
    stock: null,
    discount: 0,
    tax: 10,
    price: 120,
    category: 'Design',
    type: 'Service',
    icon: 'lucide:clock'
  }
])

const filteredItems = computed(() => {
  return items.value.filter(item =>
    (!selectedType.value || item.type === selectedType.value) &&
    (!selectedCategory.value || item.category === selectedCategory.value) &&
    (item.name.toLowerCase().includes(search.value.toLowerCase()) ||
     item.code.toLowerCase().includes(search.value.toLowerCase()))
  )
})
</script>
