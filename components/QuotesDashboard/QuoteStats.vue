<template>
  <div class="flex flex-wrap gap-5 p-5">
    <!-- Loading State -->
    <div v-if="quotesStore.isLoading" class="w-full flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00C951]"></div>
      <span class="ml-2 text-gray-600">Loading quote statistics...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="quotesStore.hasError" class="w-full bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <Icon name="lucide:alert-circle" class="w-5 h-5 text-red-500 mr-2"/>
        <span class="text-red-700">{{ quotesStore.error }}</span>
      </div>
      <button
        @click="refreshStats"
        class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
      >
        Try again
      </button>
    </div>

    <!-- Stats Cards -->
    <div
      v-else
      v-for="(stat, index) in stats"
      :key="index"
      class="bg-white p-6 rounded-xl shadow-sm w-full sm:w-[220px] flex justify-between items-center hover:shadow-lg transition-shadow duration-200"
    >
      <div>
        <p class="text-sm text-gray-500 font-medium">{{ stat.label }}</p>
        <p class="text-2xl font-bold mt-1">{{ stat.value }}</p>
        <p v-if="stat.subtext" class="text-xs text-gray-400 mt-1">{{ stat.subtext }}</p>
      </div>
      <component :is="stat.icon" :class="['w-7 h-7', stat.color]" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { Quote, CheckCheck, Send, Pen, Clock, XCircle } from 'lucide-vue-next'
import { useQuotesStore } from '~/stores/quotes'

const quotesStore = useQuotesStore()

// Fetch quote statistics on component mount
onMounted(async () => {
  try {
    await quotesStore.fetchQuoteStats()
  } catch (error) {
    console.error('Failed to load quote statistics:', error)
  }
})

// Refresh stats
const refreshStats = async () => {
  try {
    await quotesStore.fetchQuoteStats()
  } catch (error) {
    console.error('Failed to refresh quote statistics:', error)
  }
}

// Computed stats based on store data
const stats = computed(() => {
  const statsData = quotesStore.stats

  if (!statsData) {
    return [
      { label: 'Total Quotes', value: 0, icon: Quote, color: 'text-indigo-600' },
      { label: 'Accepted', value: 0, icon: CheckCheck, color: 'text-green-600' },
      { label: 'Sent', value: 0, icon: Send, color: 'text-blue-600' },
      { label: 'Draft', value: 0, icon: Pen, color: 'text-gray-600' },
      { label: 'Expired', value: 0, icon: Clock, color: 'text-orange-600' },
      { label: 'Rejected', value: 0, icon: XCircle, color: 'text-red-600' }
    ]
  }

  return [
    {
      label: 'Total Quotes',
      value: statsData.totalQuotes,
      icon: Quote,
      color: 'text-indigo-600',
      subtext: `$${(statsData.totalValue || 0).toLocaleString()}`
    },
    {
      label: 'Accepted',
      value: statsData.acceptedQuotes,
      icon: CheckCheck,
      color: 'text-green-600',
      subtext: `$${(statsData.acceptedValue || 0).toLocaleString()}`
    },
    {
      label: 'Sent',
      value: statsData.sentQuotes,
      icon: Send,
      color: 'text-blue-600'
    },
    {
      label: 'Draft',
      value: statsData.draftQuotes,
      icon: Pen,
      color: 'text-gray-600'
    },
    {
      label: 'Expired',
      value: statsData.expiredQuotes,
      icon: Clock,
      color: 'text-orange-600'
    },
    {
      label: 'Rejected',
      value: statsData.rejectedQuotes,
      icon: XCircle,
      color: 'text-red-600'
    }
  ]
})
</script>
