<template>
  <div class="p-4 sm:p-6">
    <div class="flex flex-col gap-4">
      <div class="flex flex-col sm:flex-row justify-between items-center gap-2 mb-6">

        <!-- search -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline" class="flex items-center gap-2">
              <Filter class="w-4 h-4" />
              {{ selectedStatus }}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem v-for="status in statuses" :key="status" @click="selectedStatus = status">
              {{ status }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Card class="overflow-x-auto shadow-md border border-gray-200 rounded-xl">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quote #</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valid Until</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <!-- Loading State -->
            <tr v-if="quotesStore.isLoading">
              <td colspan="7" class="px-6 py-8 text-center">
                <div class="flex justify-center items-center">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00C951]"></div>
                  <span class="ml-2 text-gray-600">Loading quotes...</span>
                </div>
              </td>
            </tr>

            <!-- Error State -->
            <tr v-else-if="quotesStore.hasError">
              <td colspan="7" class="px-6 py-8 text-center">
                <div class="text-red-600">{{ quotesStore.error }}</div>
                <button
                  @click="refreshQuotes"
                  class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  Try again
                </button>
              </td>
            </tr>

            <!-- No Data State -->
            <tr v-else-if="filteredQuotes.length === 0">
              <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                No quotes found
              </td>
            </tr>

            <!-- Quote Rows -->
            <tr v-else v-for="quote in filteredQuotes" :key="quote._id">
              <td class="px-6 py-4 whitespace-nowrap font-bold">{{ quote.quoteNumber }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ quote.clientName }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(quote.issueDate) }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(quote.validUntil) }}</td>
              <td class="px-6 py-4 whitespace-nowrap">${{ quote.total.toLocaleString() }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="statusClasses(quote.status)">{{ quote.status }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="ghost" class="h-8 w-8 p-0">
                      <MoreVertical class="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>View Quote</DropdownMenuItem>
                    <DropdownMenuItem>Edit</DropdownMenuItem>
                    <DropdownMenuItem>
                      <Download class="w-4 h-4 mr-2" /> Download PDF
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Send class="w-4 h-4 mr-2" /> Send to Client
                    </DropdownMenuItem>
                    <DropdownMenuItem class="text-red-600">Delete</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </td>
            </tr>
          </tbody>
        </table>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MoreVertical, Download, Send, Filter } from 'lucide-vue-next'
import { useQuotesStore } from '~/stores/quotes'

const searchQuery = ref('')
const selectedStatus = ref('All Statuses')
const statuses = ['All Statuses', 'Draft', 'Sent', 'Accepted', 'Expired', 'Green', 'Blue']

// Use quotes store instead of static data
const quotesStore = useQuotesStore()

// Fetch quotes on component mount
onMounted(async () => {
  try {
    await quotesStore.fetchQuotes({ limit: 10 })
  } catch (error) {
    console.error('Failed to load quotes:', error)
  }
})

// Refresh quotes
const refreshQuotes = async () => {
  try {
    await quotesStore.fetchQuotes({ limit: 10 })
  } catch (error) {
    console.error('Failed to refresh quotes:', error)
  }
}

// Format date helper
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const filteredQuotes = computed(() => {
  return quotesStore.quotes.filter((q: any) => {
    const matchesStatus = selectedStatus.value === 'All Statuses' || q.status === selectedStatus.value
    const matchesSearch = q.quoteNumber.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      q.clientName.toLowerCase().includes(searchQuery.value.toLowerCase())
    return matchesStatus && matchesSearch
  })
})

function statusClasses(status: string) {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium'

  switch (status.toLowerCase()) {
    case 'sent':
      return `${baseClasses} bg-blue-100 text-blue-800`
    case 'accepted':
      return `${baseClasses} bg-green-100 text-green-800`
    case 'draft':
      return `${baseClasses} bg-gray-100 text-gray-800`
    case 'expired':
      return `${baseClasses} bg-red-100 text-red-800`
    case 'rejected':
      return `${baseClasses} bg-red-100 text-red-800`
    default:
      return `${baseClasses} bg-gray-100 text-gray-800`
  }
}
</script>

<style scoped>
/* Add any additional styles if needed */
</style>
