<template>
  <!-- Service Revenue -->
  <div class="bg-white text-black rounded-2xl p-4 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-200">
    <div class="flex justify-between items-center">
      <h3 class="text-sm text-gray-600 font-semibold">Service Revenue</h3>
      <Icon name="lucide:trending-up" class="w-5 h-5 text-[#05DF72]"/>
    </div>
    <div class="text-2xl font-bold mt-2 text-purple-600">
      ${{ formatCurrency(dashboardStore.overview?.services?.totalRevenue || 0) }}
    </div>
    <div class="text-sm text-gray-500 mt-1">
      Total earned
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useDashboardStore } from '~/stores/dashboard'

const dashboardStore = useDashboardStore()

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}
</script>

<style>

</style>