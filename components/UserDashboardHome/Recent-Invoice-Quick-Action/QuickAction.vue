<template>
    <!-- Quick Actions -->
    <div class="bg-white text-black rounded-2xl shadow p-6">
      <h2 class="text-xl font-semibold mb-2">Quick Actions</h2>
      <p class="text-sm text-gray-500 mb-6">Frequently used actions</p>
      <div class="flex flex-col gap-3">
        <NuxtLink
          to="/invoices/new"
          class="bg-[#05DF72] text-white flex items-center gap-2 px-4 py-2 rounded-lg font-medium hover:opacity-90"
        >
          <Plus class="w-5 h-5" /> Create New Invoice
        </NuxtLink>
  
        <NuxtLink
          v-for="action in actions"
          :key="action.name"
          :to="action.path"
          :class="[
            'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition',
            $route.path === action.path
              ? 'bg-[#05DF72] text-white'
              : 'border border-gray-200 text-black hover:bg-green-200'
          ]"
        >
          <component :is="action.Icon" class="w-5 h-5" />
          {{ action.name }}
        </NuxtLink>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { Plus, User, File, Receipt } from 'lucide-vue-next'
  const actions = [
    { name: 'Add New Client', Icon: User, path: '/clients/new' },
    { name: 'Create Quote', Icon: File, path: '/quotes/quotecreate' },
    { name: 'Record Expense', Icon: Receipt, path: '/expenses/createexpenses' }
  ]
  </script>
  