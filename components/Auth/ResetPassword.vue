<template>
  <section class="h-fit bg-gray-100 font-serif">
    <div class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <img class="mx-auto h-10 w-auto" src="/assets/invoice-easy-logo.png" alt="invoice-logo" />
        <h2 class="mt-3 text-center text-2xl font-bold tracking-tight text-black">
          Reset Password
        </h2>
        <p class="mt-3 text-center text-black">Enter your new password</p>
      </div>

      <div class="mt-3 sm:mx-auto sm:w-full sm:max-w-sm">
        <div class="border border-gray-300 bg-white shadow-lg p-6 rounded-xl">
          <div class="space-y-6">
            <div>
              <label for="password" class="block text-sm font-medium text-black">New Password</label>
              <div class="mt-2">
                <input
                  id="password"
                  v-model="password"
                  type="password"
                  placeholder="Enter your new password"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
                  required
                />
              </div>
            </div>

            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-black">Confirm Password</label>
              <div class="mt-2">
                <input
                  id="confirmPassword"
                  v-model="confirmPassword"
                  type="password"
                  placeholder="Confirm your new password"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
                  required
                />
              </div>
            </div>

            <div>
              <button
                @click="handleResetPassword"
                :disabled="isSubmitting"
                class="w-full rounded-md bg-[#00C951] px-3 py-1.5 text-sm font-semibold text-white hover:bg-[#023430]"
              >
                Reset Password
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'

const password = ref('')
const confirmPassword = ref('')
const isSubmitting = ref(false)
const router = useRouter()
const toast = useToast()
const supabase = useSupabaseClient()

onMounted(async () => {
  const { data, error } = await supabase.auth.getSession()
  if (error || !data?.session) {
    toast.error('Invalid or expired reset link')
    router.push('/Auth/forgot-password')
  }
})

const handleResetPassword = async () => {
  if (!password.value || !confirmPassword.value) {
    return toast.error('Please fill all fields')
  }
  if (password.value !== confirmPassword.value) {
    return toast.error('Passwords do not match')
  }

  isSubmitting.value = true

  const { error } = await supabase.auth.updateUser({
    password: password.value
  })

  if (error) {
    toast.error(error.message)
  } else {
    toast.success('Password has been reset successfully. Please login.')
    router.push('/Auth/login')
  }

  isSubmitting.value = false
}
</script>
