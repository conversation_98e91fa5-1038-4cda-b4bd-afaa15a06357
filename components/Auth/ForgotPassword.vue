<template>
  <div>
    <Navbar />
  </div>
  <section class="h-fit bg-gray-100 font-serif">
    <div class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <img class="mx-auto h-10 w-auto" src="/assets/invoice-easy-logo.png" alt="invoice-logo" />
        <h2 class="mt-3 text-center text-2xl font-bold tracking-tight text-black">
          Forgot Password
        </h2>
        <p class="mt-3 text-center text-black">Enter your email to reset your password</p>
      </div>

      <div class="mt-3 sm:mx-auto sm:w-full sm:max-w-sm">
        <div class="border border-gray-300 bg-white shadow-lg p-6 rounded-xl">
          <div class="space-y-6">
            <div>
              <label for="email" class="block text-sm font-medium text-black">Email Address</label>
              <div class="mt-2">
                <input
                  v-model="email"
                  type="email"
                  id="email"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-black outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <div>
              <button @click="sendResetEmail" class="w-full rounded-md bg-[#00C951] px-3 py-1.5 text-sm font-semibold text-white hover:bg-[#023430]">
                Send Reset Email
              </button>
            </div>
            <p class="mt-6 text-center text-sm text-gray-500">
            Remember Your Password?
            <RouterLink to="/Auth/login" class="font-semibold text-[#00C951]">
              Login
            </RouterLink>
          </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'

const email = ref('')
const router = useRouter()
const toast = useToast()
const supabase = useSupabaseClient()

const sendResetEmail = async () => {
  if (!email.value) return toast.error('Email is required')

  const { error } = await supabase.auth.resetPasswordForEmail(email.value, {
    redirectTo: `${window.location.origin}/Auth/reset-password`,
  })

  if (error) {
    toast.error(error.message)
  } else {
    toast.success('Password reset link sent to your email!')
    router.push('/Auth/login')
  }
}
</script>
