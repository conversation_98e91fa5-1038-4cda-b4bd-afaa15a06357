<template>
  <Navbar />

  <section class="min-h-screen bg-gray-100 font-serif">
    <div class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <img class="mx-auto h-10 w-auto" src="/assets/invoice-easy-logo.png" alt="Invoice Easy Logo" />
        <h2 class="mt-3 text-center text-2xl font-bold tracking-tight text-black">
          Create an Account
        </h2>
        <p class="mt-3 text-center text-black">
          Sign up to get started with Invoice@Easy
        </p>
      </div>

      <div class="mt-6 sm:mx-auto sm:w-full sm:max-w-sm">
        <div class="border border-gray-300 bg-white shadow-lg p-6 rounded-xl">
          <form @submit.prevent="register" class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-black">Name</label>
              <input
                v-model="userData.name"
                type="text"
                placeholder="Enter your name"
                required
                class="mt-2 block w-full rounded-md bg-white px-3 py-2 text-base text-black  outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-black">Phone</label>
              <input
                v-model="userData.phone"
                type="tel"
                maxlength="10"
                placeholder="Enter 10-digit mobile number"
                required
                class="mt-2 block w-full rounded-md bg-white px-3 py-2 text-base text-black  outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-black">Email Address</label>
              <input
                v-model="userData.email"
                type="email"
                placeholder="Enter your email"
                required
                class="mt-2 block w-full rounded-md bg-white px-3 py-2 text-base text-black  outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-black">Password</label>
              <input
                v-model="userData.password"
                type="password"
                placeholder="Enter your password"
                required
                class="mt-2 block w-full rounded-md bg-white px-3 py-2 text-base text-black  outline-1 outline-gray-300 focus:outline-2 focus:outline-[#00C951]"
              />
            </div>

            <div>
              <button
                type="submit"
                class="w-full rounded-md bg-[#00C951] px-3 py-2 text-sm font-semibold text-white hover:bg-[#023430] focus:outline-2 focus:outline-[#00C951]"
                :disabled="isLoading"
              >
                <span v-if="isLoading">Signing Up...</span>
                <span v-else>Sign Up</span>
              </button>
            </div>
          </form>

          <p class="mt-6 text-center text-sm text-gray-500">
            Already have an account?
            <RouterLink to="/Auth/login" class="font-semibold text-[#00C951]">
              Login
            </RouterLink>
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useToast } from 'vue-toastification'
import { useRouter } from 'vue-router'
import { useAuthStore } from '~/stores/auth'

const toast = useToast()
const router = useRouter()
const authStore = useAuthStore()
const supabase = useSupabaseClient()

const userData = ref({
  name: '',
  email: '',
  phone: '',
  password: '',
})

const isLoading = ref(false)

const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Function to check if email is already registered
const checkUserExists = async (email: string) => {
  // Supabase Auth does not provide a safe frontend API to list users,
  // so we use a custom 'users' table if you have one
  // Replace 'users' with your actual user profile table name if applicable

  // If you don't have a users table, skip this and rely on signUp error

  const { data, error } = await supabase
    .from('users')
    .select('id')
    .eq('email', email)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows found — user does NOT exist
      return false
    }
    console.error('Error checking user existence:', error.message)
    return false
  }

  return !!data
}

const register = async () => {
  if (
    !userData.value.name ||
    !userData.value.phone ||
    !userData.value.email ||
    !userData.value.password
  ) {
    toast.error('Please fill all fields.')
    return
  }

  if (!validateEmail(userData.value.email)) {
    toast.error('Please enter a valid email address.')
    return
  }

  if (!userData.value.phone.match(/^\d{10}$/)) {
    toast.error('Please enter a valid 10-digit mobile number.')
    return
  }

  isLoading.value = true

  // OPTIONAL: check user existence only if you have a users table
  // Comment this out if you do not have one or want to rely on supabase error
  /*
  const exists = await checkUserExists(userData.value.email)
  if (exists) {
    isLoading.value = false
    toast.error('Email already registered. Please login or use a different email.')
    return
  }
  */

  const { data, error } = await supabase.auth.signUp({
    email: userData.value.email,
    password: userData.value.password,
    options: {
      data: {
        name: userData.value.name,
        phone: `+91${userData.value.phone}`,
      },
    },
  })

  isLoading.value = false

  if (error) {
    // Supabase returns error if email already exists or other issues
    toast.error(`Registration failed: ${error.message}`)
  } else {
    await authStore.fetchUser()

    toast.success('Registration successful! Please verify your email.')
    router.push('/Auth/login')
  }
}
</script>
