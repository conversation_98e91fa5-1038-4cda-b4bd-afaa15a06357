<template>
  <div class="max-w-5xl mx-auto bg-white p-8 rounded-lg border mt-4 shadow-xl">
    <h2 class="text-2xl font-semibold mb-6">Expense Details</h2>
    <form class="space-y-6">
      <!-- Row 1 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">Expense Number</label>
          <input
            type="text"
            v-model="expense.expenseNumber"
            class="w-full border rounded-md px-4 py-2"
            readonly
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Date</label>
          <input
            type="date"
            v-model="expense.date"
            class="w-full border rounded-md px-4 py-2"
          />
        </div>
      </div>

      <!-- Row 2 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">Vendor/Supplier</label>
          <input
            type="text"
            v-model="expense.vendor"
            placeholder="Enter vendor name"
            class="w-full border rounded-md px-4 py-2"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Category</label>
          <select v-model="expense.category" class="w-full border rounded-md px-4 py-2">
            <option value="" disabled>Select category</option>
            <option>Food</option>
            <option>Travel</option>
            <option>Utilities</option>
          </select>
        </div>
      </div>

      <!-- Row 3 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">Amount (₹)</label>
          <input
            type="number"
            v-model="expense.amount"
            placeholder="0.00"
            class="w-full border rounded-md px-4 py-2"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Payment Method</label>
          <select v-model="expense.paymentMethod" class="w-full border rounded-md px-4 py-2">
            <option value="" disabled>Select payment method</option>
            <option>Cash</option>
            <option>Card</option>
            <option>UPI</option>
          </select>
        </div>
      </div>

      <!-- Description -->
      <div>
        <label class="block text-sm font-medium mb-1">Description</label>
        <textarea
          v-model="expense.description"
          rows="3"
          placeholder="Describe the expense..."
          class="w-full border rounded-md px-4 py-2"
        ></textarea>
      </div>

      <!-- Receipt and Notes -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">Receipt Number (Optional)</label>
          <input
            v-model="expense.receiptNumber"
            placeholder="Receipt or reference number"
            class="w-full border rounded-md px-4 py-2"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Additional Notes (Optional)</label>
          <textarea
            v-model="expense.notes"
            rows="1"
            placeholder="Any additional notes..."
            class="w-full border rounded-md px-4 py-2"
          ></textarea>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex justify-end gap-3 pt-4">
        <button
          type="button"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
        >
          <X class="inline-block w-4 h-4 mr-1" /> Cancel
        </button>
        <button
          type="submit"
          class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md flex items-center"
        >
          <Save class="w-4 h-4 mr-1" /> Create Expense
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Save, X } from 'lucide-vue-next'

const expense = ref({
  expenseNumber: 'EXP-662892',
  date: '2025-06-11',
  vendor: '',
  category: '',
  amount: '',
  paymentMethod: '',
  description: '',
  receiptNumber: '',
  notes: '',
})
</script>
