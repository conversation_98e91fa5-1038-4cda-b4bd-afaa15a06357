<template>
  <div class="rounded-xl border bg-white p-5 shadow-xl">
    <div class="flex justify-between items-start">
      <div>
        <h2 class="text-lg font-semibold">{{ id }}</h2>
        <p class="text-sm text-gray-500">{{ vendor }}</p>
      </div>
      <span
        :class="[
          'text-xs font-medium px-2 py-1 rounded-full flex items-center gap-1 capitalize',
          status === 'paid' && 'bg-green-100 text-green-600',
          status === 'pending' && 'bg-yellow-100 text-yellow-600',
          status === 'approved' && 'bg-blue-100 text-blue-600',
          status === 'rejected' && 'bg-red-100 text-red-600'
        ]"
      >
        <component :is="statusIcon" class="w-4 h-4" />
        {{ status }}
      </span>
    </div>

    <div class="mt-4 text-sm space-y-1">
      <p><span class="text-gray-500">Category:</span> <span class="font-medium">{{ category }}</span></p>
      <p><span class="text-gray-500">Amount:</span> <span class="text-indigo-600 font-semibold">₹{{ amount.toLocaleString() }}</span></p>
      <p><span class="text-gray-500">Date:</span> {{ date }}</p>
      <p class="text-gray-600">{{ note }}</p>
    </div>

    <div class="mt-4 flex items-center justify-start gap-4 text-gray-400">
      <Eye class="hover:text-indigo-600 cursor-pointer" />
      <Pencil class="hover:text-yellow-600 cursor-pointer" />
      <Download class="hover:text-blue-600 cursor-pointer" />
      <Trash class="hover:text-red-600 cursor-pointer" />
    </div>
  </div>
</template>

<script setup>
import { Eye, Pencil, Download, Trash, CheckCheck, Clock, XCircle, CheckCircle2 } from 'lucide-vue-next'

const props = defineProps({
  id: String,
  vendor: String,
  category: String,
  amount: Number,
  date: String,
  note: String,
  status: String
})

const statusIcon = computed(() => ({
  paid: CheckCheck,
  pending: Clock,
  rejected: XCircle,
  approved: CheckCircle2
}[props.status]))
</script>
