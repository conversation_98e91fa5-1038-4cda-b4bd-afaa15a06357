<template>
  <div>
    <div class="fixed top-4 right-4 z-50">
      <div
        @click="toggleTheme"
        class="relative w-16 h-8 rounded-full cursor-pointer transition-all duration-500 overflow-hidden"
        :class="isDark ? 'bg-gradient-to-b from-[#8c96a7] to-[#0a0c0f]' : 'bg-gradient-to-b from-[#94be89] to-[#05ec18]'"
      >
        <!-- Toggle Circle -->
        <div
  class="absolute top-1 left-1 w-6 h-6 rounded-full transition-all duration-500 flex items-center justify-center"
  :class="[
    isDark
      ? 'translate-x-0 bg-white text-gray-700 shadow-inner'
      : 'translate-x-10 bg-[#f39c12] text-white shadow-md'
  ]"
>
  <i :class="isDark ? 'i-lucide-moon w-4 h-4' : 'i-lucide-sun w-4 h-4'"></i>
</div>

      </div>
    </div>

    <slot />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const isDark = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  isDark.value = savedTheme === 'dark'
  document.documentElement.classList.toggle('dark', isDark.value)
})
</script>


// plugins/pinia.client.ts

import { defineNuxtPlugin } from '#app'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

export default defineNuxtPlugin((nuxtApp) => {
  // Create the Pinia instance
  const pinia = createPinia()
  // Use the persisted state plugin
  pinia.use(piniaPluginPersistedstate)

  // Use Pinia in the Vue app
  nuxtApp.vueApp.use(pinia)
})
