// composables/useSupabaseClient.ts
import { createClient } from '@supabase/supabase-js'

export const useCustomSupabaseClient = () => {
  const config = useRuntimeConfig()
  
  // Create Supabase client with custom options for development
  const supabase = createClient(
    config.public.supabase.url,
    config.public.supabase.anonKey,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      },
      global: {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        }
      },
      // Custom fetch function to handle CORS in development
      ...(process.dev && {
        fetch: async (url: string, options: any = {}) => {
          try {
            const response = await fetch(url, {
              ...options,
              mode: 'cors',
              credentials: 'include',
              headers: {
                ...options.headers,
                'Access-Control-Allow-Origin': '*',
              }
            })
            return response
          } catch (error: any) {
            if (error.message.includes('CORS')) {
              console.warn('🔄 CORS error detected, using fallback auth method')
              // Return a mock response for development
              return new Response(JSON.stringify({ 
                error: { message: 'CORS error - using fallback' } 
              }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
              })
            }
            throw error
          }
        }
      })
    }
  )

  return supabase
}
