/**
 * Composable to handle search field management after client operations
 * This allows auto-populating search fields with email addresses to find new clients
 */

import { nextTick } from 'vue'

export const useSearchManagement = () => {
  /**
   * Populate search inputs with the specified value (e.g., email address)
   * @param value - The value to set in search inputs (e.g., email address)
   */
  const populateSearchInputs = async (value: string) => {
    await nextTick()

    // Find all search input fields
    const searchSelectors = [
      'input[placeholder*="Search"]',
      'input[placeholder*="search"]',
      'input[type="search"]',
      '.search-input',
      '#search',
      '[data-search]'
    ]

    const searchInputs = document.querySelectorAll(searchSelectors.join(', '))

    searchInputs.forEach((input: any) => {
      if (input && input.tagName === 'INPUT') {
        input.value = value
        // Trigger input event to update any reactive bindings
        input.dispatchEvent(new Event('input', { bubbles: true }))
      }
    })
  }

  /**
   * Clear all search inputs that might contain the specified value
   * @param value - The value to clear from search inputs (e.g., email address)
   */
  const clearSearchInputs = async (value?: string) => {
    await nextTick()

    // Find all search input fields
    const searchSelectors = [
      'input[placeholder*="Search"]',
      'input[placeholder*="search"]',
      'input[type="search"]',
      '.search-input',
      '#search',
      '[data-search]'
    ]

    const searchInputs = document.querySelectorAll(searchSelectors.join(', '))

    searchInputs.forEach((input: any) => {
      if (input && input.tagName === 'INPUT') {
        // If a specific value is provided, only clear if it matches
        if (value) {
          if (input.value === value || input.value.includes(value)) {
            input.value = ''
            // Trigger input event to update any reactive bindings
            input.dispatchEvent(new Event('input', { bubbles: true }))
          }
        } else {
          // Clear all search inputs
          input.value = ''
          input.dispatchEvent(new Event('input', { bubbles: true }))
        }
      }
    })
  }

  /**
   * Populate search inputs after a delay (useful for form submissions)
   * @param value - The value to set in search inputs
   * @param delay - Delay in milliseconds (default: 100ms)
   */
  const populateSearchInputsDelayed = (value: string, delay: number = 100) => {
    setTimeout(() => {
      populateSearchInputs(value)
    }, delay)
  }

  /**
   * Clear search inputs after a delay (useful for form submissions)
   * @param delay - Delay in milliseconds (default: 100ms)
   * @param value - Optional specific value to clear
   */
  const clearSearchInputsDelayed = (delay: number = 100, value?: string) => {
    setTimeout(() => {
      clearSearchInputs(value)
    }, delay)
  }

  /**
   * Populate specific search field by selector
   * @param selector - CSS selector for the search field
   * @param value - The value to set
   */
  const populateSpecificSearch = (selector: string, value: string) => {
    const input = document.querySelector(selector) as HTMLInputElement
    if (input) {
      input.value = value
      input.dispatchEvent(new Event('input', { bubbles: true }))
    }
  }

  /**
   * Clear specific search field by selector
   * @param selector - CSS selector for the search field
   */
  const clearSpecificSearch = (selector: string) => {
    const input = document.querySelector(selector) as HTMLInputElement
    if (input) {
      input.value = ''
      input.dispatchEvent(new Event('input', { bubbles: true }))
    }
  }

  /**
   * Populate Vue reactive search variables
   * This should be called with the actual reactive refs
   */
  const populateReactiveSearch = (value: string, ...searchRefs: any[]) => {
    searchRefs.forEach(ref => {
      if (ref && typeof ref.value !== 'undefined') {
        ref.value = value
      }
    })
  }

  /**
   * Clear Vue reactive search variables
   * This should be called with the actual reactive refs
   */
  const clearReactiveSearch = (...searchRefs: any[]) => {
    searchRefs.forEach(ref => {
      if (ref && typeof ref.value !== 'undefined') {
        ref.value = ''
      }
    })
  }

  return {
    populateSearchInputs,
    populateSearchInputsDelayed,
    populateSpecificSearch,
    populateReactiveSearch,
    clearSearchInputs,
    clearSearchInputsDelayed,
    clearSpecificSearch,
    clearReactiveSearch
  }
}
