{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@nuxt-alt/http": "^1.7.10", "@nuxt/content": "3.4.0", "@nuxt/eslint": "1.3.0", "@nuxt/fonts": "0.11.1", "@nuxt/icon": "1.12.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.5", "@nuxt/test-utils": "3.17.2", "@nuxt/ui": "3.0.2", "@pinia/nuxt": "^0.11.0", "@supabase/supabase-js": "^2.49.7", "@tailwindcss/vite": "^4.1.4", "@unhead/vue": "^2.0.0-rc.8", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "eslint": "^9.0.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-vue-next": "^0.488.0", "nuxt": "^3.16.2", "nuxt-lucide-icons": "1.0.5", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "reka-ui": "^2.2.0", "shadcn-nuxt": "2.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-toastification": "2.0.0-rc.5", "vuedraggable": "^4.1.0", "yup": "^1.6.1"}, "packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee", "devDependencies": {"@nuxtbase/auth-ui-vue": "^0.3.7", "@nuxtjs/supabase": "^1.5.1", "@supabase/auth-ui-shared": "^0.1.8", "@types/sortablejs": "^1.15.8", "typescript": "^5.8.3"}}