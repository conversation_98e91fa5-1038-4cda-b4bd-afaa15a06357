// Types for Reports API
export interface ReportsSummary {
  totalRevenue: number
  totalInvoices: number
  avgInvoiceValue: number
  newClients: number
}

export interface TopClient {
  name: string
  type: string
  revenue: number
  invoices: number
}

export interface DetailedClient {
  name: string
  email: string
  type: string
  invoices: number
  revenue: number
  avgInvoice: number
  status: string
}

export interface ClientOption {
  _id: string
  name: string
}

export interface ReportsData {
  summary: ReportsSummary
  topClients: TopClient[]
  detailedClients: DetailedClient[]
  clients: ClientOption[]
}

export interface ReportsResponse {
  success: boolean
  data: ReportsData
  message?: string
}

export interface ReportsParams {
  dateRange?: string
  clientId?: string
  reportType?: string
}

class ReportsApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const authStore = useAuthStore()
    
    if (!authStore.session?.access_token) {
      throw new Error('No authentication token available')
    }
    
    return {
      'Authorization': `Bearer ${authStore.session.access_token}`,
      'Content-Type': 'application/json'
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getClientReports(params?: ReportsParams): Promise<ReportsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const url = `${this.baseURL}/reports/clients${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      return this.handleResponse<ReportsResponse>(response)
    } catch (error) {
      console.error('Get client reports error:', error)
      throw error
    }
  }

  // Fallback method for testing without authentication
  async getClientReportsTest(params?: ReportsParams): Promise<ReportsResponse> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const url = `${this.baseURL}/clients/reports/test${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return this.handleResponse<ReportsResponse>(response)
    } catch (error) {
      console.error('Get client reports test error:', error)
      throw error
    }
  }
}

// Export singleton instance
export const reportsApi = new ReportsApiService()

// Export types
export type { ReportsResponse, ReportsData, ReportsParams }
