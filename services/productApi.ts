// Types for Product API
export interface ProductImage {
  url: string
  alt?: string
  isPrimary: boolean
}

export interface ProductDimensions {
  length?: number
  width?: number
  height?: number
  unit: 'cm' | 'inch' | 'mm'
}

export interface ProductWeight {
  value?: number
  unit: 'kg' | 'gram' | 'pound' | 'ounce'
}

export interface Product {
  _id: string
  userId: string
  productId: string
  name: string
  description?: string
  category: string
  sku?: string
  barcode?: string
  costPrice: number
  sellingPrice: number
  currency: string
  stock: number
  minStockLevel: number
  maxStockLevel?: number
  unit: 'piece' | 'kg' | 'gram' | 'liter' | 'meter' | 'box' | 'pack' | 'dozen' | 'other'
  taxRate: number
  taxInclusive: boolean
  status: 'active' | 'inactive' | 'discontinued'
  isVisible: boolean
  images: ProductImage[]
  dimensions?: ProductDimensions
  weight?: ProductWeight
  tags: string[]
  notes?: string
  totalSold: number
  totalRevenue: number
  lastSoldDate?: string
  createdAt: string
  updatedAt: string
}

export interface ProductStats {
  totalProducts: number
  activeProducts: number
  inactiveProducts: number
  lowStockProducts: number
  totalStock: number
  totalValue: number
  totalRevenue: number
}

export interface ProductsResponse {
  success: boolean
  count: number
  totalProducts: number
  totalPages: number
  currentPage: number
  data: Product[]
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalProducts: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface ProductResponse {
  success: boolean
  data: Product
  message?: string
}

export interface ProductStatsResponse {
  success: boolean
  data: ProductStats
}

export interface ProductCategoriesResponse {
  success: boolean
  data: string[]
}

export interface ProductParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
  sort?: string
  lowStock?: boolean
}

export interface StockUpdateData {
  quantity: number
  operation: 'add' | 'subtract'
}

class ProductApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const authStore = useAuthStore()
    
    if (!authStore.session?.access_token) {
      throw new Error('No authentication token available')
    }
    
    return {
      'Authorization': `Bearer ${authStore.session.access_token}`,
      'Content-Type': 'application/json'
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getProducts(params?: ProductParams): Promise<ProductsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }
      
      const url = `${this.baseURL}/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching products with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ProductsResponse>(response)
    } catch (error) {
      console.error('Get products error:', error)
      throw error
    }
  }

  async getProduct(id: string): Promise<ProductResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products/${id}`

      console.log('🔗 Fetching product with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ProductResponse>(response)
    } catch (error) {
      console.error('Get product error:', error)
      throw error
    }
  }

  async createProduct(productData: Omit<Product, '_id' | 'userId' | 'productId' | 'totalSold' | 'totalRevenue' | 'createdAt' | 'updatedAt'>): Promise<ProductResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products`

      console.log('🔗 Creating product with URL:', url)
      console.log('📊 Product data:', productData)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(productData)
      })
      
      return this.handleResponse<ProductResponse>(response)
    } catch (error) {
      console.error('Create product error:', error)
      throw error
    }
  }

  async updateProduct(id: string, productData: Partial<Product>): Promise<ProductResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products/${id}`

      console.log('🔗 Updating product with URL:', url)
      console.log('📊 Product data:', productData)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(productData)
      })
      
      return this.handleResponse<ProductResponse>(response)
    } catch (error) {
      console.error('Update product error:', error)
      throw error
    }
  }

  async deleteProduct(id: string): Promise<ProductResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products/${id}`

      console.log('🔗 Deleting product with URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers
      })
      
      return this.handleResponse<ProductResponse>(response)
    } catch (error) {
      console.error('Delete product error:', error)
      throw error
    }
  }

  async getProductStats(): Promise<ProductStatsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products/stats`

      console.log('🔗 Fetching product stats with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ProductStatsResponse>(response)
    } catch (error) {
      console.error('Get product stats error:', error)
      throw error
    }
  }

  async getProductCategories(): Promise<ProductCategoriesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products/categories`

      console.log('🔗 Fetching product categories with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ProductCategoriesResponse>(response)
    } catch (error) {
      console.error('Get product categories error:', error)
      throw error
    }
  }

  async updateProductStock(id: string, stockData: StockUpdateData): Promise<ProductResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/products/${id}/stock`

      console.log('🔗 Updating product stock with URL:', url)
      console.log('📊 Stock data:', stockData)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(stockData)
      })
      
      return this.handleResponse<ProductResponse>(response)
    } catch (error) {
      console.error('Update product stock error:', error)
      throw error
    }
  }
}

export const productApi = new ProductApiService()
