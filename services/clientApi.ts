// services/clientApi.ts
import { useAuthStore } from '~/stores/auth'
import { useRuntimeConfig } from '#app'

interface Client {
  _id?: string
  clientId?: string
  type?: 'Individual' | 'Business'
  name: string
  email: string
  phone?: string
  company?: {
    name?: string
    website?: string
    taxId?: string
  }
  address?: {
    street?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  currency?: string
  paymentTerms?: string
  creditLimit?: number
  status?: 'Active' | 'Inactive' | 'Suspended'
  notes?: string
  tags?: string[]
  totalInvoices?: number
  totalAmount?: number
  outstandingAmount?: number
  lastInvoiceDate?: string
  createdAt?: string
  updatedAt?: string
}

interface ClientsResponse {
  success: boolean
  count: number
  totalClients: number
  totalPages: number
  currentPage: number
  data: Client[]
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalClients: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

interface ClientResponse {
  success: boolean
  data: Client
  message?: string
}

interface ClientStatsResponse {
  success: boolean
  data: {
    totalClients: number
    activeClients: number
    inactiveClients: number
    totalInvoiceAmount: number
    totalOutstanding: number
  }
}

interface ClientReportsResponse {
  success: boolean
  data: {
    summary: {
      totalRevenue: number
      totalInvoices: number
      avgInvoiceValue: number
      newClients: number
    }
    topClients: Array<{
      name: string
      type: string
      revenue: number
      invoices: number
    }>
    detailedClients: Array<{
      name: string
      email: string
      type: string
      invoices: number
      revenue: number
      avgInvoice: number
      status: string
    }>
    clients: Array<{
      _id: string
      name: string
    }>
  }
}

interface ApiError {
  success: false
  message: string
  code?: string
  errors?: string[]
}

class ClientApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const authStore = useAuthStore()
    
    if (!authStore.session?.access_token) {
      throw new Error('No authentication token available')
    }
    
    return {
      'Authorization': `Bearer ${authStore.session.access_token}`,
      'Content-Type': 'application/json'
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getClients(params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
    sort?: string
  }): Promise<ClientsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }
      
      const url = `${this.baseURL}/clients${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching clients with URL:', url) // Debug log

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ClientsResponse>(response)
    } catch (error) {
      console.error('Get clients error:', error)
      throw error
    }
  }

  async getClient(id: string): Promise<ClientResponse> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/clients/${id}`, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ClientResponse>(response)
    } catch (error) {
      console.error('Get client error:', error)
      throw error
    }
  }

  async createClient(clientData: Omit<Client, '_id'>): Promise<ClientResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/clients`

      console.log('🔗 Creating client with URL:', url) // Debug log
      console.log('📊 Client data:', clientData) // Debug log

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(clientData)
      })
      
      return this.handleResponse<ClientResponse>(response)
    } catch (error) {
      console.error('Create client error:', error)
      throw error
    }
  }

  async updateClient(id: string, clientData: Partial<Client>): Promise<ClientResponse> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/clients/${id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(clientData)
      })
      
      return this.handleResponse<ClientResponse>(response)
    } catch (error) {
      console.error('Update client error:', error)
      throw error
    }
  }

  async deleteClient(id: string): Promise<ClientResponse> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/clients/${id}`, {
        method: 'DELETE',
        headers
      })
      
      return this.handleResponse<ClientResponse>(response)
    } catch (error) {
      console.error('Delete client error:', error)
      throw error
    }
  }

  async getClientStats(): Promise<ClientStatsResponse> {
    try {
      const headers = await this.getAuthHeaders()

      const response = await fetch(`${this.baseURL}/clients/stats`, {
        method: 'GET',
        headers
      })

      return this.handleResponse<ClientStatsResponse>(response)
    } catch (error) {
      console.error('Get client stats error:', error)
      throw error
    }
  }

  async getClientReports(params?: {
    dateRange?: string
    clientId?: string
    reportType?: string
  }): Promise<ClientReportsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const url = `${this.baseURL}/clients/reports${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching client reports with URL:', url) // Debug log

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      return this.handleResponse<ClientReportsResponse>(response)
    } catch (error) {
      console.error('Get client reports error:', error)
      throw error
    }
  }

  async getClientReportsTest(params?: {
    dateRange?: string
    clientId?: string
    reportType?: string
  }): Promise<ClientReportsResponse> {
    try {
      const queryParams = new URLSearchParams()

      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }

      const url = `${this.baseURL}/clients/reports/test${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return this.handleResponse<ClientReportsResponse>(response)
    } catch (error) {
      console.error('Get client reports test error:', error)
      throw error
    }
  }

  async testAuth(): Promise<any> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/auth-test/protected`, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse(response)
    } catch (error) {
      console.error('Test auth error:', error)
      throw error
    }
  }
}

// Export singleton instance
export const clientApi = new ClientApiService()

// Export types
export type { Client, ClientsResponse, ClientResponse, ClientStatsResponse, ApiError }
