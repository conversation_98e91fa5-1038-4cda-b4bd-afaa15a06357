import type { Invoice } from '~/services/invoiceApi'

export interface PDFDownloadOptions {
  filename?: string
  format?: 'A4' | 'Letter'
  method?: 'browser' | 'html'
}

export class PDFDownloader {
  private invoice: Invoice
  private options: PDFDownloadOptions

  constructor(invoice: Invoice, options: PDFDownloadOptions = {}) {
    this.invoice = invoice
    this.options = {
      filename: `Invoice-${invoice.invoiceNumber}.pdf`,
      format: 'A4',
      method: 'browser',
      ...options
    }
  }

  // Main method to download PDF
  async downloadPDF(): Promise<void> {
    try {
      if (this.options.method === 'html') {
        await this.downloadAsHTML()
      } else {
        await this.downloadViaBrowser()
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      throw new Error('Failed to download PDF. Please try again.')
    }
  }

  // Method 1: Download via browser's print-to-PDF functionality
  private async downloadViaBrowser(): Promise<void> {
    // Create a new window optimized for PDF generation
    const pdfWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=no,menubar=no,toolbar=no')
    
    if (!pdfWindow) {
      throw new Error('Unable to open PDF window. Please check your popup blocker settings.')
    }

    // Generate PDF-optimized HTML
    const htmlContent = this.generatePDFHTML()
    
    // Write content to the window
    pdfWindow.document.write(htmlContent)
    pdfWindow.document.close()

    // Wait for content to load
    await this.waitForLoad(pdfWindow)

    // Set document title for PDF filename
    pdfWindow.document.title = this.options.filename || `Invoice-${this.invoice.invoiceNumber}`

    // Add download instructions
    this.addDownloadInstructions(pdfWindow)

    // Focus and trigger print dialog
    pdfWindow.focus()
    
    // Auto-trigger print dialog
    setTimeout(() => {
      pdfWindow.print()
    }, 1000)
  }

  // Method 2: Download as HTML file (fallback)
  private async downloadAsHTML(): Promise<void> {
    const htmlContent = this.generatePDFHTML()
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = this.options.filename?.replace('.pdf', '.html') || `Invoice-${this.invoice.invoiceNumber}.html`
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 1000)
  }

  // Generate PDF-optimized HTML
  private generatePDFHTML(): string {
    const { invoice } = this
    
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.options.filename || `Invoice-${invoice.invoiceNumber}`}</title>
        <style>
          ${this.getPDFStyles()}
        </style>
      </head>
      <body>
        <div class="pdf-container">
          ${this.generateInvoiceContent()}
        </div>
        <script>
          window.onload = function() {
            window.focus();
            // Remove instructions after 15 seconds
            setTimeout(function() {
              const instructions = document.getElementById('download-instructions');
              if (instructions) instructions.remove();
            }, 15000);
          };
        </script>
      </body>
      </html>
    `
  }

  // Generate complete invoice content
  private generateInvoiceContent(): string {
    const { invoice } = this
    
    return `
      <!-- Header -->
      <div class="header">
        <div class="company-info">
          <h1>Invoice @Easy</h1>
          <p>Professional Invoice Management</p>
        </div>
        <div class="invoice-title">
          <h2>INVOICE</h2>
          <div class="invoice-number">${invoice.invoiceNumber}</div>
        </div>
      </div>

      <!-- Invoice Details -->
      <div class="invoice-details">
        <div class="bill-to">
          <div class="section-title">Bill To</div>
          <div class="client-name">${invoice.clientName}</div>
          <div class="client-details">
            <p>${invoice.clientEmail}</p>
            ${invoice.clientPhone ? `<p>${invoice.clientPhone}</p>` : ''}
            ${this.generateClientAddress()}
          </div>
        </div>
        <div class="invoice-info">
          <div class="section-title">Invoice Details</div>
          <p><strong>Issue Date:</strong> ${this.formatDate(invoice.issueDate)}</p>
          <p><strong>Due Date:</strong> ${this.formatDate(invoice.dueDate)}</p>
          <p><strong>Type:</strong> ${invoice.invoiceType.charAt(0).toUpperCase() + invoice.invoiceType.slice(1)}</p>
          ${invoice.paymentTerms ? `<p><strong>Payment Terms:</strong> ${invoice.paymentTerms}</p>` : ''}
          <div class="status-badges">
            ${invoice.isPaid ? '<span class="status-badge status-paid">PAID</span>' : ''}
            ${this.isOverdue() ? '<span class="status-badge status-overdue">OVERDUE</span>' : ''}
            ${!invoice.isPaid && !this.isOverdue() ? '<span class="status-badge status-sent">SENT</span>' : ''}
          </div>
        </div>
      </div>

      <!-- Items Table -->
      <table class="items-table">
        <thead>
          <tr>
            <th>Description</th>
            <th style="text-align: center;">Quantity</th>
            <th style="text-align: center;">Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${invoice.items.map(item => `
            <tr>
              <td>
                <div class="item-description">${item.description}</div>
                ${item.type ? `<div class="item-type">${item.type}</div>` : ''}
              </td>
              <td style="text-align: center;">${item.quantity}</td>
              <td style="text-align: center;">₹${this.formatCurrency(item.unitPrice)}</td>
              <td>₹${this.formatCurrency(item.total || (item.quantity * item.unitPrice))}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <!-- Totals -->
      <div class="totals-section">
        <div class="totals-table">
          <div class="totals-row">
            <span class="totals-label">Subtotal:</span>
            <span class="totals-amount">₹${this.formatCurrency(invoice.subtotal)}</span>
          </div>
          ${invoice.discountRate > 0 ? `
            <div class="totals-row">
              <span class="totals-label">Discount (${invoice.discountRate}%):</span>
              <span class="totals-amount">-₹${this.formatCurrency(invoice.discountAmount)}</span>
            </div>
          ` : ''}
          ${invoice.taxRate > 0 ? `
            <div class="totals-row">
              <span class="totals-label">Tax (${invoice.taxRate}%):</span>
              <span class="totals-amount">₹${this.formatCurrency(invoice.taxAmount)}</span>
            </div>
          ` : ''}
          <div class="totals-row totals-final">
            <span class="totals-label">TOTAL:</span>
            <span class="totals-amount">₹${this.formatCurrency(invoice.total)}</span>
          </div>
        </div>
      </div>

      <!-- Notes -->
      ${invoice.notes ? `
        <div class="notes-section">
          <div class="section-title">Notes</div>
          <div class="notes-content">${invoice.notes}</div>
        </div>
      ` : ''}

      <!-- Footer -->
      <div class="footer">
        <p>Generated on ${this.formatDate(new Date().toISOString())} | Invoice @Easy - Professional Invoice Management</p>
      </div>
    `
  }

  // Helper methods
  private generateClientAddress(): string {
    const address = this.invoice.clientAddress
    if (!address) return ''

    const parts = []
    if (address.street) parts.push(`<p>${address.street}</p>`)
    
    const cityStateZip = [address.city, address.state, address.zipCode].filter(Boolean).join(', ')
    if (cityStateZip) parts.push(`<p>${cityStateZip}</p>`)
    
    if (address.country) parts.push(`<p>${address.country}</p>`)
    
    return parts.join('')
  }

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  private formatCurrency(amount: number): string {
    return amount.toFixed(2)
  }

  private isOverdue(): boolean {
    if (this.invoice.isPaid) return false
    const currentDate = new Date()
    const dueDate = new Date(this.invoice.dueDate)
    return currentDate > dueDate
  }

  private async waitForLoad(window: Window): Promise<void> {
    return new Promise((resolve) => {
      if (window.document.readyState === 'complete') {
        resolve()
      } else {
        window.onload = () => resolve()
        setTimeout(resolve, 2000) // Fallback timeout
      }
    })
  }

  private addDownloadInstructions(pdfWindow: Window): void {
    const instructionDiv = pdfWindow.document.createElement('div')
    instructionDiv.id = 'download-instructions'
    instructionDiv.innerHTML = `
      <div style="
        position: fixed; 
        top: 20px; 
        right: 20px; 
        background: #4CAF50; 
        color: white; 
        padding: 15px; 
        border-radius: 8px; 
        z-index: 1000; 
        font-family: Arial, sans-serif; 
        font-size: 13px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        max-width: 250px;
      ">
        <strong>📄 Download as PDF:</strong><br><br>
        <strong>Chrome/Edge:</strong><br>
        • Press Ctrl+P (⌘+P on Mac)<br>
        • Select "Save as PDF"<br>
        • Click "Save"<br><br>
        <strong>Firefox:</strong><br>
        • Press Ctrl+P (⌘+P on Mac)<br>
        • Choose "Microsoft Print to PDF"<br>
        • Click "Print"
        <div style="margin-top: 10px; font-size: 11px; opacity: 0.9;">
          This message will disappear in 15 seconds
        </div>
      </div>
    `
    pdfWindow.document.body.appendChild(instructionDiv)
  }

  private getPDFStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .pdf-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
        background: white;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 40px;
        border-bottom: 2px solid #000;
        padding-bottom: 20px;
      }

      .company-info h1 {
        font-size: 28px;
        font-weight: bold;
        color: #000;
        margin-bottom: 5px;
      }

      .company-info p {
        color: #333;
        font-size: 14px;
      }

      .invoice-title {
        text-align: right;
      }

      .invoice-title h2 {
        font-size: 36px;
        font-weight: bold;
        color: #000;
        margin-bottom: 5px;
      }

      .invoice-number {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .invoice-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40px;
      }

      .bill-to, .invoice-info {
        flex: 1;
      }

      .bill-to {
        margin-right: 40px;
      }

      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #000;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .client-name {
        font-size: 18px;
        font-weight: bold;
        color: #000;
        margin-bottom: 8px;
      }

      .client-details p, .invoice-info p {
        margin-bottom: 5px;
        color: #333;
      }

      .status-badges {
        margin-top: 10px;
      }

      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border: 1px solid #000;
        border-radius: 3px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
        margin-right: 8px;
        background: white;
        color: #000;
      }

      .status-paid {
        background-color: #e8f5e8 !important;
        border-color: #4caf50 !important;
        color: #2e7d32 !important;
      }

      .status-overdue {
        background-color: #ffebee !important;
        border-color: #f44336 !important;
        color: #c62828 !important;
      }

      .status-sent {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
        color: #1565c0 !important;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 30px;
      }

      .items-table th {
        background-color: #f0f0f0;
        padding: 15px 12px;
        text-align: left;
        font-weight: bold;
        color: #000;
        border: 1px solid #000;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .items-table th:last-child,
      .items-table td:last-child {
        text-align: right;
      }

      .items-table td {
        padding: 12px;
        border: 1px solid #000;
        color: #000;
      }

      .item-description {
        font-weight: 500;
        color: #000;
      }

      .item-type {
        font-size: 10px;
        color: #666;
        text-transform: capitalize;
      }

      .totals-section {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 40px;
      }

      .totals-table {
        width: 300px;
      }

      .totals-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #ccc;
      }

      .totals-final {
        border-bottom: 2px solid #000 !important;
        border-top: 2px solid #000 !important;
        font-weight: bold !important;
        font-size: 16px !important;
        color: #000 !important;
        padding: 15px 0 !important;
        margin-top: 5px;
      }

      .totals-label {
        color: #000;
      }

      .totals-amount {
        font-weight: 500;
        color: #000;
      }

      .notes-section {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #000;
      }

      .notes-content {
        color: #000;
        line-height: 1.6;
        white-space: pre-wrap;
      }

      .footer {
        margin-top: 60px;
        padding-top: 20px;
        border-top: 1px solid #000;
        text-align: center;
        color: #333;
        font-size: 11px;
      }

      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        
        .pdf-container {
          padding: 20px;
        }
        
        @page {
          margin: 0.5in;
          size: A4;
        }

        #download-instructions {
          display: none !important;
        }
      }

      @media screen {
        .pdf-container {
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
          margin: 20px;
        }
      }
    `
  }
}

// Utility function for easy use
export const downloadInvoicePDF = async (invoice: Invoice, options?: PDFDownloadOptions): Promise<void> => {
  const downloader = new PDFDownloader(invoice, options)
  await downloader.downloadPDF()
}

export default PDFDownloader
