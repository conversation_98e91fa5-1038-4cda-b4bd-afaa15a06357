import jsPDF from 'jspdf'
import type { Invoice } from '~/services/invoiceApi'

export interface DirectPDFOptions {
  filename?: string
  format?: 'a4' | 'letter'
  orientation?: 'portrait' | 'landscape'
}

export class DirectPDFDownloader {
  private invoice: Invoice
  private options: DirectPDFOptions
  private doc: jsPDF

  constructor(invoice: Invoice, options: DirectPDFOptions = {}) {
    this.invoice = invoice
    this.options = {
      filename: `Invoice-${invoice.invoiceNumber}.pdf`,
      format: 'a4',
      orientation: 'portrait',
      ...options
    }
    
    // Initialize jsPDF
    this.doc = new jsPDF({
      orientation: this.options.orientation,
      unit: 'mm',
      format: this.options.format
    })
  }

  // Main method to generate and download PDF directly
  async downloadPDF(): Promise<void> {
    try {
      // Validate invoice data
      this.validateInvoiceData()

      // Generate PDF content
      this.generatePDFContent()

      // Direct download without opening new window
      this.doc.save(this.options.filename!)

    } catch (error) {
      console.error('Error generating PDF:', error)

      // Provide specific error messages
      if (error instanceof Error) {
        throw error
      }

      throw new Error('Failed to generate PDF. Please try again.')
    }
  }

  // Validate invoice data before PDF generation
  private validateInvoiceData(): void {
    const { invoice } = this

    if (!invoice) {
      throw new Error('Invoice data is missing')
    }

    if (!invoice.invoiceNumber) {
      throw new Error('Invoice number is required')
    }

    if (!invoice.clientName) {
      throw new Error('Client name is required')
    }

    if (!invoice.items || invoice.items.length === 0) {
      throw new Error('Invoice must have at least one item')
    }

    // Validate required numeric fields
    if (typeof invoice.total !== 'number' || invoice.total < 0) {
      throw new Error('Invalid invoice total amount')
    }
  }

  // Generate complete PDF content
  private generatePDFContent(): void {
    const { invoice } = this
    
    // Set up page margins and dimensions
    const pageWidth = this.doc.internal.pageSize.getWidth()
    const pageHeight = this.doc.internal.pageSize.getHeight()
    const margin = 20
    const contentWidth = pageWidth - (margin * 2)
    
    let yPosition = margin

    // Header Section
    yPosition = this.addHeader(yPosition, contentWidth, margin)
    
    // Invoice Details Section
    yPosition = this.addInvoiceDetails(yPosition, contentWidth, margin)
    
    // Items Table
    yPosition = this.addItemsTable(yPosition, contentWidth, margin)
    
    // Totals Section
    yPosition = this.addTotals(yPosition, contentWidth, margin)
    
    // Notes Section (if exists)
    if (invoice.notes) {
      yPosition = this.addNotes(yPosition, contentWidth, margin)
    }
    
    // Footer
    this.addFooter(pageHeight, contentWidth, margin)
  }

  // Add header section
  private addHeader(yPosition: number, contentWidth: number, margin: number): number {
    const { invoice } = this

    // Add header background
    this.doc.setFillColor(248, 250, 252) // Light blue-gray background
    this.doc.rect(margin - 5, yPosition - 5, contentWidth + 10, 35, 'F')

    // Company name and title
    this.doc.setFontSize(28)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(30, 58, 138) // Professional blue
    this.doc.text('Invoice @Easy', margin, yPosition + 8)

    // Invoice title (right aligned)
    this.doc.setFontSize(36)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(15, 23, 42) // Dark slate
    this.doc.text('INVOICE', margin + contentWidth, yPosition + 8, { align: 'right' })

    yPosition += 15

    // Company subtitle
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(71, 85, 105) // Slate gray
    this.doc.text('Professional Invoice Management', margin, yPosition + 8)

    // Invoice number (right aligned)
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(15, 23, 42) // Dark slate
    this.doc.text(invoice.invoiceNumber, margin + contentWidth, yPosition + 8, { align: 'right' })

    yPosition += 25

    // Add decorative line separator
    this.doc.setLineWidth(2)
    this.doc.setDrawColor(30, 58, 138) // Professional blue
    this.doc.line(margin, yPosition, margin + contentWidth, yPosition)

    return yPosition + 20
  }

  // Add invoice details section
  private addInvoiceDetails(yPosition: number, contentWidth: number, margin: number): number {
    const { invoice } = this
    const leftColumnWidth = contentWidth * 0.5
    const rightColumnStart = margin + leftColumnWidth + 10
    
    // Bill To section
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('BILL TO', margin, yPosition)
    
    // Invoice Details section (right side)
    this.doc.text('INVOICE DETAILS', rightColumnStart, yPosition)
    
    yPosition += 8
    
    // Client information (left side)
    this.doc.setFontSize(14)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text(invoice.clientName, margin, yPosition)
    
    yPosition += 6
    
    this.doc.setFontSize(10)
    this.doc.setFont('helvetica', 'normal')
    this.doc.text(invoice.clientEmail, margin, yPosition)
    
    if (invoice.clientPhone) {
      yPosition += 4
      this.doc.text(invoice.clientPhone, margin, yPosition)
    }
    
    // Invoice information (right side)
    let rightYPosition = yPosition - (invoice.clientPhone ? 10 : 6)
    
    this.doc.setFontSize(10)
    this.doc.text(`Issue Date: ${this.formatDate(invoice.issueDate)}`, rightColumnStart, rightYPosition)
    rightYPosition += 4
    
    this.doc.text(`Due Date: ${this.formatDate(invoice.dueDate)}`, rightColumnStart, rightYPosition)
    rightYPosition += 4
    
    this.doc.text(`Type: ${invoice.invoiceType.charAt(0).toUpperCase() + invoice.invoiceType.slice(1)}`, rightColumnStart, rightYPosition)
    rightYPosition += 4
    
    // Status display
    const status = this.getDisplayStatus()
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(0, 0, 0)

    let statusText = `Status: ${status.charAt(0).toUpperCase() + status.slice(1)}`
    this.doc.text(statusText, rightColumnStart, rightYPosition)

    // Add overdue indicator if applicable
    if (this.isOverdue()) {
      rightYPosition += 5
      this.doc.setTextColor(200, 0, 0) // Red color for overdue
      this.doc.setFont('helvetica', 'bold')
      this.doc.text('⚠ OVERDUE', rightColumnStart, rightYPosition)
      this.doc.setTextColor(0, 0, 0) // Reset to black
    }
    
    return Math.max(yPosition, rightYPosition) + 15
  }

  // Add items table
  private addItemsTable(yPosition: number, contentWidth: number, margin: number): number {
    const { invoice } = this
    const tableStartY = yPosition
    const rowHeight = 10
    const headerHeight = 12

    // Table headers with better proportions
    const colWidths = {
      description: contentWidth * 0.45,
      quantity: contentWidth * 0.15,
      unitPrice: contentWidth * 0.2,
      total: contentWidth * 0.2
    }

    // Header background with professional styling
    this.doc.setFillColor(30, 58, 138) // Professional blue
    this.doc.rect(margin, yPosition, contentWidth, headerHeight, 'F')

    // Header borders
    this.doc.setLineWidth(1)
    this.doc.setDrawColor(30, 58, 138)
    this.doc.rect(margin, yPosition, contentWidth, headerHeight)

    // Add vertical lines for columns
    this.doc.setDrawColor(255, 255, 255) // White lines
    this.doc.setLineWidth(0.5)

    let xPos = margin + colWidths.description
    this.doc.line(xPos, yPosition, xPos, yPosition + headerHeight)

    xPos += colWidths.quantity
    this.doc.line(xPos, yPosition, xPos, yPosition + headerHeight)

    xPos += colWidths.unitPrice
    this.doc.line(xPos, yPosition, xPos, yPosition + headerHeight)

    // Header text with white color
    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(255, 255, 255) // White text

    let xPosition = margin + 4
    this.doc.text('DESCRIPTION', xPosition, yPosition + 8)

    xPosition = margin + colWidths.description + (colWidths.quantity / 2)
    this.doc.text('QTY', xPosition, yPosition + 8, { align: 'center' })

    xPosition = margin + colWidths.description + colWidths.quantity + (colWidths.unitPrice / 2)
    this.doc.text('UNIT PRICE', xPosition, yPosition + 8, { align: 'center' })

    xPosition = margin + colWidths.description + colWidths.quantity + colWidths.unitPrice + (colWidths.total / 2)
    this.doc.text('TOTAL', xPosition, yPosition + 8, { align: 'center' })

    yPosition += headerHeight

    // Table rows
    this.doc.setFont('helvetica', 'normal')
    this.doc.setFontSize(9)

    invoice.items.forEach((item, index) => {
      // Row background (alternating with subtle colors)
      if (index % 2 === 1) {
        this.doc.setFillColor(248, 250, 252) // Very light blue-gray
        this.doc.rect(margin, yPosition, contentWidth, rowHeight, 'F')
      } else {
        this.doc.setFillColor(255, 255, 255) // White
        this.doc.rect(margin, yPosition, contentWidth, rowHeight, 'F')
      }

      // Row borders with subtle styling
      this.doc.setLineWidth(0.2)
      this.doc.setDrawColor(226, 232, 240) // Light gray
      this.doc.rect(margin, yPosition, contentWidth, rowHeight)

      // Add vertical lines for columns
      let xPos = margin + colWidths.description
      this.doc.line(xPos, yPosition, xPos, yPosition + rowHeight)

      xPos += colWidths.quantity
      this.doc.line(xPos, yPosition, xPos, yPosition + rowHeight)

      xPos += colWidths.unitPrice
      this.doc.line(xPos, yPosition, xPos, yPosition + rowHeight)

      // Row content with proper alignment and styling
      this.doc.setTextColor(15, 23, 42) // Dark slate
      this.doc.setFontSize(10)
      this.doc.setFont('helvetica', 'normal')

      // Description (left aligned)
      let xPosition = margin + 4
      this.doc.text(item.description, xPosition, yPosition + 6.5)

      // Quantity (center aligned)
      xPosition = margin + colWidths.description + (colWidths.quantity / 2)
      this.doc.text(item.quantity.toString(), xPosition, yPosition + 6.5, { align: 'center' })

      // Unit Price (right aligned)
      xPosition = margin + colWidths.description + colWidths.quantity + colWidths.unitPrice - 4
      this.doc.text(`Rs.${this.formatCurrency(item.unitPrice)}`, xPosition, yPosition + 6.5, { align: 'right' })

      // Total (right aligned with bold styling)
      const itemTotal = item.total || (item.quantity * item.unitPrice)
      this.doc.setFont('helvetica', 'bold')
      xPosition = margin + contentWidth - 4
      this.doc.text(`Rs.${this.formatCurrency(itemTotal)}`, xPosition, yPosition + 6.5, { align: 'right' })
      this.doc.setFont('helvetica', 'normal') // Reset font

      yPosition += rowHeight
    })

    // Final table border
    this.doc.setLineWidth(0.5)
    this.doc.line(margin, yPosition, margin + contentWidth, yPosition)

    return yPosition + 15
  }

  // Add totals section
  private addTotals(yPosition: number, contentWidth: number, margin: number): number {
    const { invoice } = this
    const totalsWidth = 100
    const totalsStartX = margin + contentWidth - totalsWidth

    // Add background for totals section
    const totalsHeight = 50
    this.doc.setFillColor(248, 250, 252) // Light blue-gray background
    this.doc.rect(totalsStartX - 5, yPosition - 5, totalsWidth + 5, totalsHeight, 'F')

    // Add border around totals
    this.doc.setLineWidth(0.5)
    this.doc.setDrawColor(226, 232, 240)
    this.doc.rect(totalsStartX - 5, yPosition - 5, totalsWidth + 5, totalsHeight)

    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(15, 23, 42) // Dark slate

    // Subtotal
    this.doc.text('Subtotal:', totalsStartX, yPosition + 5)
    this.doc.text(`Rs.${this.formatCurrency(invoice.subtotal)}`, totalsStartX + totalsWidth - 10, yPosition + 5, { align: 'right' })
    yPosition += 7

    // Discount (if applicable)
    if (invoice.discountRate > 0) {
      this.doc.text(`Discount (${invoice.discountRate}%):`, totalsStartX, yPosition + 5)
      this.doc.setTextColor(220, 38, 38) // Red for discount
      this.doc.text(`-Rs.${this.formatCurrency(invoice.discountAmount)}`, totalsStartX + totalsWidth - 10, yPosition + 5, { align: 'right' })
      this.doc.setTextColor(15, 23, 42) // Reset color
      yPosition += 7
    }

    // Tax (if applicable)
    if (invoice.taxRate > 0) {
      this.doc.text(`Tax (${invoice.taxRate}%):`, totalsStartX, yPosition + 5)
      this.doc.text(`Rs.${this.formatCurrency(invoice.taxAmount)}`, totalsStartX + totalsWidth - 10, yPosition + 5, { align: 'right' })
      yPosition += 7
    }

    // Total line separator with professional styling
    this.doc.setLineWidth(1.5)
    this.doc.setDrawColor(30, 58, 138) // Professional blue
    this.doc.line(totalsStartX, yPosition + 7, totalsStartX + totalsWidth - 10, yPosition + 7)
    yPosition += 12

    // Final total with enhanced styling
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(30, 58, 138) // Professional blue
    this.doc.text('TOTAL:', totalsStartX, yPosition + 5)
    this.doc.text(`Rs.${this.formatCurrency(invoice.total)}`, totalsStartX + totalsWidth - 10, yPosition + 5, { align: 'right' })

    this.doc.setTextColor(0, 0, 0) // Reset color
    return yPosition + 25
  }

  // Add notes section
  private addNotes(yPosition: number, contentWidth: number, margin: number): number {
    const { invoice } = this
    
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('NOTES', margin, yPosition)
    
    yPosition += 8
    
    this.doc.setFontSize(10)
    this.doc.setFont('helvetica', 'normal')
    
    // Split notes into lines to fit within page width
    const lines = this.doc.splitTextToSize(invoice.notes!, contentWidth)
    this.doc.text(lines, margin, yPosition)
    
    return yPosition + (lines.length * 4) + 10
  }

  // Add footer
  private addFooter(pageHeight: number, contentWidth: number, margin: number): void {
    const footerY = pageHeight - 15
    
    // Footer line
    this.doc.setLineWidth(0.3)
    this.doc.line(margin, footerY - 5, margin + contentWidth, footerY - 5)
    
    // Footer text
    this.doc.setFontSize(8)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(100, 100, 100)
    
    const footerText = `Generated on ${this.formatDate(new Date().toISOString())} | Invoice @Easy - Professional Invoice Management`
    this.doc.text(footerText, margin + (contentWidth / 2), footerY, { align: 'center' })
    
    // Reset text color
    this.doc.setTextColor(0, 0, 0)
  }

  // Helper methods
  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0)
  }

  private getDisplayStatus(): string {
    const { invoice } = this
    
    if (invoice.isPaid) return 'paid'
    if (invoice.status === 'draft' || invoice.status === 'cancelled') return invoice.status
    return invoice.status === 'overdue' ? 'sent' : invoice.status
  }

  private isOverdue(): boolean {
    const { invoice } = this
    if (invoice.isPaid) return false
    
    const currentDate = new Date()
    const dueDate = new Date(invoice.dueDate)
    return currentDate > dueDate && invoice.status !== 'draft' && invoice.status !== 'cancelled'
  }
}

// Utility function for easy use
export const downloadInvoicePDFDirect = async (invoice: Invoice, options?: DirectPDFOptions): Promise<void> => {
  const downloader = new DirectPDFDownloader(invoice, options)
  await downloader.downloadPDF()
}

export default DirectPDFDownloader
