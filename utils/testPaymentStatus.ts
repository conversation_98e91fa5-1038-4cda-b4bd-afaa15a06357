// Test utility for payment status functionality
export const testPaymentStatusData = {
  // Test invoice data for payment status testing
  testInvoice: {
    _id: 'test-invoice-payment-123',
    invoiceNumber: 'TEST-PAY-001',
    clientName: 'Test Payment Client',
    clientEmail: '<EMAIL>',
    clientPhone: '******-0199',
    items: [
      {
        description: 'Payment Status Test Service',
        quantity: 1,
        unitPrice: 1000,
        total: 1000
      }
    ],
    subtotal: 1000,
    taxRate: 18,
    taxAmount: 180,
    discountRate: 0,
    discountAmount: 0,
    total: 1180,
    status: 'sent',
    isPaid: false,
    issueDate: '2025-06-15',
    dueDate: '2025-07-15',
    invoiceType: 'service'
  }
}

// Test function to validate payment status update logic
export const validatePaymentStatusLogic = (invoice: any, newPaidStatus: boolean): boolean => {
  console.log('🧪 Testing payment status logic...')
  console.log(`📄 Invoice: ${invoice.invoiceNumber}`)
  console.log(`💰 Current: isPaid=${invoice.isPaid}, status=${invoice.status}`)
  console.log(`🔄 New paid status: ${newPaidStatus}`)

  // Test the expected status logic
  let expectedStatus: string
  if (newPaidStatus) {
    expectedStatus = 'paid'
  } else {
    // If marking as unpaid, determine status based on due date
    const currentDate = new Date()
    const dueDate = new Date(invoice.dueDate)
    
    if (invoice.status === 'draft') {
      expectedStatus = 'draft'
    } else if (currentDate > dueDate) {
      expectedStatus = 'overdue'
    } else {
      expectedStatus = 'sent'
    }
  }

  console.log(`📊 Expected status: ${expectedStatus}`)
  
  return true
}

// Test function to simulate payment status update
export const simulatePaymentStatusUpdate = async (invoice: any, newPaidStatus: boolean): Promise<any> => {
  console.log('🔄 Simulating payment status update...')
  
  // Simulate the backend logic
  const currentDate = new Date()
  const dueDate = new Date(invoice.dueDate)
  
  let newStatus: string
  if (newPaidStatus) {
    newStatus = 'paid'
  } else {
    if (invoice.status === 'draft') {
      newStatus = 'draft'
    } else if (currentDate > dueDate) {
      newStatus = 'overdue'
    } else {
      newStatus = 'sent'
    }
  }

  const updatedInvoice = {
    ...invoice,
    isPaid: newPaidStatus,
    status: newStatus
  }

  console.log(`✅ Simulated update result:`)
  console.log(`   - isPaid: ${updatedInvoice.isPaid}`)
  console.log(`   - status: ${updatedInvoice.status}`)

  return updatedInvoice
}

// Test function for dashboard statistics calculation
export const testDashboardStatistics = (invoices: any[]): any => {
  console.log('📊 Testing dashboard statistics calculation...')
  
  const stats = {
    totalInvoices: invoices.length,
    totalAmount: invoices.reduce((sum, inv) => sum + inv.total, 0),
    
    // Use isPaid field for accurate paid calculations
    paidAmount: invoices.filter(inv => inv.isPaid || inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0),
    
    // Pending includes sent and overdue (unpaid invoices)
    pendingAmount: invoices.filter(inv => !inv.isPaid && inv.status !== 'draft' && inv.status !== 'cancelled').reduce((sum, inv) => sum + inv.total, 0),
    
    // Overdue amount (unpaid and past due date)
    overdueAmount: invoices.filter(inv => {
      if (inv.isPaid || inv.status === 'draft' || inv.status === 'cancelled') return false
      const currentDate = new Date()
      const dueDate = new Date(inv.dueDate)
      return currentDate > dueDate
    }).reduce((sum, inv) => sum + inv.total, 0),
    
    // Status counts
    draftCount: invoices.filter(inv => inv.status === 'draft').length,
    sentCount: invoices.filter(inv => inv.status === 'sent').length,
    paidCount: invoices.filter(inv => inv.isPaid || inv.status === 'paid').length,
    overdueCount: invoices.filter(inv => {
      if (inv.isPaid || inv.status === 'draft' || inv.status === 'cancelled') return false
      const currentDate = new Date()
      const dueDate = new Date(inv.dueDate)
      return currentDate > dueDate
    }).length
  }

  console.log('📈 Dashboard Statistics:')
  console.log(`   - Total Invoices: ${stats.totalInvoices}`)
  console.log(`   - Total Amount: Rs.${stats.totalAmount.toFixed(2)}`)
  console.log(`   - Paid Amount: Rs.${stats.paidAmount.toFixed(2)}`)
  console.log(`   - Pending Amount: Rs.${stats.pendingAmount.toFixed(2)}`)
  console.log(`   - Overdue Amount: Rs.${stats.overdueAmount.toFixed(2)}`)
  console.log(`   - Paid Count: ${stats.paidCount}`)
  console.log(`   - Sent Count: ${stats.sentCount}`)
  console.log(`   - Overdue Count: ${stats.overdueCount}`)

  return stats
}

// Test function for payment status scenarios
export const testPaymentStatusScenarios = (): void => {
  console.log('🧪 Testing Payment Status Scenarios...')
  console.log('=====================================')

  // Scenario 1: Mark sent invoice as paid
  console.log('Scenario 1: Mark sent invoice as paid')
  const sentInvoice = { ...testPaymentStatusData.testInvoice, status: 'sent', isPaid: false }
  const paidResult = simulatePaymentStatusUpdate(sentInvoice, true)
  console.log(`Result: ${paidResult.status === 'paid' && paidResult.isPaid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Scenario 2: Mark paid invoice as unpaid (not overdue)
  console.log('Scenario 2: Mark paid invoice as unpaid (not overdue)')
  const paidInvoice = { ...testPaymentStatusData.testInvoice, status: 'paid', isPaid: true }
  const unpaidResult = simulatePaymentStatusUpdate(paidInvoice, false)
  console.log(`Result: ${unpaidResult.status === 'sent' && !unpaidResult.isPaid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Scenario 3: Mark paid invoice as unpaid (overdue)
  console.log('Scenario 3: Mark paid invoice as unpaid (overdue)')
  const overdueDate = new Date()
  overdueDate.setDate(overdueDate.getDate() - 10) // 10 days ago
  const overdueInvoice = { 
    ...testPaymentStatusData.testInvoice, 
    status: 'paid', 
    isPaid: true,
    dueDate: overdueDate.toISOString().split('T')[0]
  }
  const overdueResult = simulatePaymentStatusUpdate(overdueInvoice, false)
  console.log(`Result: ${overdueResult.status === 'overdue' && !overdueResult.isPaid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Scenario 4: Dashboard statistics with mixed invoices
  console.log('Scenario 4: Dashboard statistics calculation')
  const mixedInvoices = [
    { ...testPaymentStatusData.testInvoice, _id: '1', status: 'paid', isPaid: true, total: 1000 },
    { ...testPaymentStatusData.testInvoice, _id: '2', status: 'sent', isPaid: false, total: 2000 },
    { ...testPaymentStatusData.testInvoice, _id: '3', status: 'overdue', isPaid: false, total: 1500, dueDate: '2025-06-01' },
    { ...testPaymentStatusData.testInvoice, _id: '4', status: 'draft', isPaid: false, total: 800 }
  ]
  const stats = testDashboardStatistics(mixedInvoices)
  const expectedPaidAmount = 1000
  const expectedPendingAmount = 3500 // sent + overdue
  console.log(`Result: ${stats.paidAmount === expectedPaidAmount && stats.pendingAmount === expectedPendingAmount ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  console.log('=====================================')
  console.log('🏁 Payment Status Tests Complete!')
}

// Console test function for browser testing
export const runPaymentStatusTests = async (): Promise<void> => {
  console.log('🧪 Running Payment Status Tests...')
  console.log('=====================================')

  try {
    // Test payment status scenarios
    testPaymentStatusScenarios()

    // Test validation logic
    console.log('Testing validation logic...')
    const validationResult = validatePaymentStatusLogic(testPaymentStatusData.testInvoice, true)
    console.log(`Validation: ${validationResult ? '✅ PASS' : '❌ FAIL'}`)

    console.log('')
    console.log('✅ All payment status tests completed successfully!')

  } catch (error) {
    console.error('❌ Payment status tests failed:', error)
  }
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testPaymentStatus = runPaymentStatusTests
  (window as any).testPaymentStatusScenarios = testPaymentStatusScenarios
  (window as any).testDashboardStatistics = testDashboardStatistics
}
