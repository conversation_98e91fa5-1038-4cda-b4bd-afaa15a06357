
export const dashboardLinks = [
    {
        label: 'Dashboard',
        icon: 'lucide:layout-dashboard',
        link: '/',
        color: 'text-green-400'
      },
    {
        label: 'Clients',
        icon:'lucide:users',
        link: '/clients',
        color: 'text-green-400',
        // children: [
        //   {
        //     label: 'Add Client',
        //     icon: 'lucide:user-plus',
        //     link: '/clients/new',
        //     color: 'text-green-400'
        //   }
        // ]
    },
       {
         label: 'Invoices',
        icon:'lucide:notepad-text',
        link: '/invoices',
        color: 'text-green-400',
      },
    {
        label: 'Quotes',
        icon:'lucide:notebook-pen',
        link: '/quotes',
        color: 'text-green-400'
    },
    {
        label: 'Expenses',
        icon:'lucide:dollar-sign',
        link: '/expenses',
        color: 'text-green-400'
    },
    // {
    //     label: 'Projects',
    //     icon:'lucide:file',
    //     link: '/projects',
    //     color: 'text-green-400'
    // },
    {
        label: 'products & Services',
        icon:'lucide:package',
        link: '/products-service',
        color: 'text-green-400'
    },
    {
        label: 'Payments',
        icon: 'lucide:badge-indian-rupee',
        link: '/payments',
        color: 'text-green-400',
      },
    // {
    //     label: 'Time Tracking',
    //     icon:'lucide:timer',
    //     link: '/time-tracking',
    //     color: 'text-green-400'
    // },
    {
        label: 'Reports',
        icon:'lucide:chart-area',
        link: '/reports',
        color: 'text-green-400'
    },
    {
        label: 'Settings',
        icon: 'lucide:settings',
        link: '/settings', // ignored if children present
        color: 'text-green-400',
      },
]

export const tools = [
    {
        label: 'Customer',
        icon:'lucide:user',
        link: '/conversation',
        color: 'text-sky-500',  
        bgColor: 'bg-green-400',
    },
    {
        label: 'Products',
        icon:'lucide:layout-dashboard',
        link: '/dashboard',
        color: 'text-sky-500'
    },
    {
        label: 'Services',
        icon:'lucide:layout-dashboard',
        link: '/dashboard',
        color: 'text-sky-500'
    },
    {
        label: 'Quotes',
        icon:'lucide:layout-dashboard',
        link: '/dashboard',
        color: 'text-sky-500'
    },
    {
        label: 'Services',
        icon:'lucide:layout-dashboard',
        link: '/dashboard',
        color: 'text-sky-500'
    },
    {
        label: 'Expenses',
        icon:'lucide:layout-dashboard',
        link: '/dashboard',
        color: 'text-sky-500'
    },
    {
        label: 'Reports',
        icon:'lucide:radius',
        link: '/dashboard',
        color: 'text-sky-500'
    },
    {
        label: 'Settings',
        icon:'lucide:settings',
        link: '/dashboard',
        color: 'text-sky-500'
    },
]


// utils/index.ts

export const premiumModal = [
  {
    title: 'Unlimited Invoices',
    icon: 'lucide:file-text',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
  },
  {
    title: 'Email Sending Enabled',
    icon: 'lucide:send',
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    title: 'Custom Themes',
    icon: 'lucide:paintbrush',
    color: 'text-pink-600',
    bgColor: 'bg-pink-100',
  },
  {
    title: 'Analytics Dashboard',
    icon: 'lucide:bar-chart',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    title: 'Export to PDF/Excel',
    icon: 'lucide:download',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
  },
  {
    title: 'Team Collaboration',
    icon: 'lucide:users',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
  {
    title: 'Priority Support',
    icon: 'lucide:life-buoy',
    color: 'text-red-600',
    bgColor: 'bg-red-100',
  },
]

export const pricingPlans = {
  monthly: {
    label: 'Monthly Plan',
    price: 299,
    billingNote: 'Billed monthly',
  },
  yearly: {
    label: 'Yearly Plan',
    price: 249,
    billingNote: 'Billed ₹2988/year',
  },
}

