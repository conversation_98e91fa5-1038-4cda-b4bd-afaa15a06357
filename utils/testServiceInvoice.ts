// Test utility for service invoice functionality
export const testServiceInvoiceData = {
  // Test client data
  testClient: {
    _id: 'test-client-123',
    name: 'Test Client Company',
    email: '<EMAIL>',
    phone: '******-0123',
    address: {
      street: '123 Business Street',
      city: 'Business City',
      state: 'BC',
      zipCode: '12345',
      country: 'USA'
    }
  },

  // Test service data
  testServices: [
    {
      _id: 'service-1',
      name: 'Web Development',
      description: 'Custom website development',
      price: 1500,
      category: 'Development'
    },
    {
      _id: 'service-2', 
      name: 'SEO Optimization',
      description: 'Search engine optimization services',
      price: 800,
      category: 'Marketing'
    }
  ],

  // Test invoice data
  testInvoice: {
    clientName: 'Test Client Company',
    clientEmail: '<EMAIL>',
    clientPhone: '******-0123',
    clientAddress: {
      street: '123 Business Street',
      city: 'Business City',
      state: 'BC',
      zipCode: '12345',
      country: 'USA'
    },
    items: [
      {
        description: 'Web Development - Custom website with responsive design',
        quantity: 1,
        unitPrice: 1500,
        type: 'service',
        serviceId: 'service-1'
      },
      {
        description: 'SEO Optimization - 3 months of SEO services',
        quantity: 3,
        unitPrice: 800,
        type: 'service', 
        serviceId: 'service-2'
      }
    ],
    subtotal: 3900,
    taxRate: 18,
    taxAmount: 702,
    discountRate: 5,
    discountAmount: 195,
    total: 4407,
    invoiceType: 'service',
    status: 'sent',
    issueDate: '2025-06-15',
    dueDate: '2025-07-15',
    notes: 'Payment terms: Net 30 days. Late fees may apply.',
    paymentTerms: 'Net 30'
  }
}

// Test function to validate service invoice data structure
export const validateServiceInvoiceData = (invoiceData: any): boolean => {
  const required = [
    'clientName',
    'clientEmail', 
    'items',
    'invoiceType',
    'dueDate'
  ]

  // Check required fields
  for (const field of required) {
    if (!invoiceData[field]) {
      console.error(`Missing required field: ${field}`)
      return false
    }
  }

  // Check invoice type
  if (invoiceData.invoiceType !== 'service') {
    console.error('Invoice type must be "service"')
    return false
  }

  // Check items
  if (!Array.isArray(invoiceData.items) || invoiceData.items.length === 0) {
    console.error('Items must be a non-empty array')
    return false
  }

  // Check each item
  for (const item of invoiceData.items) {
    if (!item.description || !item.quantity || !item.unitPrice) {
      console.error('Each item must have description, quantity, and unitPrice')
      return false
    }
    if (item.type !== 'service') {
      console.error('All items must have type "service"')
      return false
    }
  }

  console.log('✅ Service invoice data validation passed')
  return true
}

// Test function to check client information completeness
export const validateClientInformation = (clientData: any): boolean => {
  if (!clientData) {
    console.error('Client data is missing')
    return false
  }

  const required = ['name', 'email']
  for (const field of required) {
    if (!clientData[field]) {
      console.error(`Missing required client field: ${field}`)
      return false
    }
  }

  console.log('✅ Client information validation passed')
  return true
}

// Test function for PDF generation
export const testServiceInvoicePDF = async (): Promise<boolean> => {
  try {
    const { downloadInvoicePDFDirect } = await import('~/utils/directPdfDownloader')
    
    // Create test invoice with proper structure
    const testInvoice = {
      ...testServiceInvoiceData.testInvoice,
      invoiceNumber: 'TEST-SRV-001',
      _id: 'test-service-invoice-123'
    }

    console.log('🧪 Testing service invoice PDF generation...')
    console.log('📄 Test invoice data:', testInvoice)

    // Validate data before PDF generation
    if (!validateServiceInvoiceData(testInvoice)) {
      throw new Error('Service invoice data validation failed')
    }

    if (!validateClientInformation(testInvoice)) {
      throw new Error('Client information validation failed')
    }

    // Generate PDF
    await downloadInvoicePDFDirect(testInvoice, {
      filename: 'Test-Service-Invoice.pdf'
    })

    console.log('✅ Service invoice PDF generated successfully!')
    return true

  } catch (error) {
    console.error('❌ Service invoice PDF test failed:', error)
    return false
  }
}

// Console test function
export const runServiceInvoiceTests = async (): Promise<void> => {
  console.log('🧪 Running Service Invoice Tests...')
  console.log('=====================================')

  // Test 1: Data structure validation
  console.log('Test 1: Data Structure Validation')
  const dataValid = validateServiceInvoiceData(testServiceInvoiceData.testInvoice)
  console.log(`Result: ${dataValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Test 2: Client information validation  
  console.log('Test 2: Client Information Validation')
  const clientValid = validateClientInformation(testServiceInvoiceData.testClient)
  console.log(`Result: ${clientValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  // Test 3: PDF generation
  console.log('Test 3: PDF Generation')
  const pdfValid = await testServiceInvoicePDF()
  console.log(`Result: ${pdfValid ? '✅ PASS' : '❌ FAIL'}`)
  console.log('')

  console.log('=====================================')
  console.log('🏁 Service Invoice Tests Complete!')
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testServiceInvoice = runServiceInvoiceTests
  (window as any).testServiceInvoicePDF = testServiceInvoicePDF
}
